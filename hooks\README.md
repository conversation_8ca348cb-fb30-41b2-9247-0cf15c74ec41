# Hooks 使用说明

本项目包含了一系列自定义 Hooks，用于处理各种业务逻辑和通用功能。以下是各个 Hook 的详细说明：

## 业务相关 Hooks

### useOrder

订单管理相关的 Hook，用于处理订单数据。

```typescript
const { fetchOrderData } = useOrder();
// 获取订单列表
const res = await fetchOrderData({ current: 1, size: 10 });
```

### useAttendance

考勤管理相关的 Hook，提供考勤数据的获取和处理功能。

```=typescript
const {
  fetchAttendanceList, // 获取考勤列表
  handleAttendanceTime, // 处理考勤时间
  getWorkStatus, // 获取工作状态
  attendanceData, // 考勤数据
  attendanceStats, // 考勤统计
  todayAttendance, // 今日考勤
} = useAttendance();
```

### useSchedual

排班管理相关的 Hook，用于获取和处理排班数据。

```typescript
const { fetchSchedual, selectSchedual } = useSchedual();
// 获取排班数据
await fetchSchedual();
// 获取当前排班
const currentSchedual = selectSchedual.value;
```

### useClothe

服装管理相关的 Hook，用于获取服装列表数据。

```typescript
const { fetchClothesData } = useClothe();
// 获取服装列表
const res = await fetchClothesData(params);
```

## 工具类 Hooks

### useDateFormat

日期格式化工具 Hook，提供日期相关的格式化功能。

```typescript
const { format, getMonthRangeString, parseDate } = useDateFormat();
// 格式化日期
const formattedDate = format(new Date(), "YYYY-MM-DD");
// 获取月份范围
const monthRange = getMonthRangeString(new Date());
```

### useUtils

通用工具 Hook，提供文件上传等功能。

```typescript
const { uploadImage, takePhotoAsFile } = useUtils();
// 拍照并上传
const imageUrl = await takePhotoAsFile(tempFilePath);
```

### useQQMap

腾讯地图相关功能 Hook，提供地址转换等功能。

```typescript
const { convertAddress } = useQQMap();
// 转换地址
const address = await convertAddress({ latitude, longitude });
```

### useNavigate

路由导航 Hook，提供页面跳转和事件通信功能。

```typescript
const { navigateTo, onEvent } = useNavigate();
// 页面跳转
await navigateTo("order", "refresh", params);
// 监听事件
onEvent("refresh", (params) => {
  // 处理参数
});
```

### useOnLaunch

小程序启动相关的 Hook，用于获取全局属性。

```typescript
const { $isResolve, $onLaunch } = useOnLaunch();
```

## 使用注意事项

1. 所有 Hook 都遵循 Vue 3 的组合式 API 规范
2. 使用 Hook 时需要在 `setup` 函数或 `<script setup>` 中调用
3. 部分 Hook 依赖于 Pinia store，使用前确保相关 store 已正确配置
4. 异步操作相关的 Hook 都返回 Promise，需要使用 async/await 处理

## 最佳实践

1. 在组件中使用 Hook 时，建议使用解构赋值获取需要的功能
2. 对于异步操作，建议使用 try/catch 进行错误处理
3. 在组件卸载时，记得清理相关的监听器和定时器
4. 合理使用计算属性和响应式数据，避免不必要的重复计算
