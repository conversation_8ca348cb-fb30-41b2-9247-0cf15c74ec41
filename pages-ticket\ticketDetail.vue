<template>
  <BasicContainer>
    <view class="detail-content">
      <view class="detail-section">
        <text class="section-title">基本信息</text>
        <view class="detail-row">
          <text class="detail-label">工单类型：</text>
          <text class="detail-value">{{
            ticketMap[ticket.type] || "未知类型"
          }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">工单状态：</text>
          <text class="detail-value" :class="getStatusClass(ticket.status)">
            {{ getStatusText(ticket.status) }}
          </text>
        </view>
        <view class="detail-row">
          <text class="detail-label">水洗码：</text>
          <text class="detail-value">{{ ticket.washCode || "暂无" }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">订单号：</text>
          <text class="detail-value">{{ ticket.orderSn || "暂无" }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">创建时间：</text>
          <text class="detail-value">{{ formatTime(ticket.createTime) }}</text>
        </view>
      </view>

      <view class="detail-section" v-if="ticket.details">
        <text class="section-title">问题描述</text>
        <text class="problem-description">{{ ticket.details }}</text>
      </view>

      <view
        class="detail-section"
        v-if="ticket.images && ticket.images.length > 0"
      >
        <text class="section-title">相关图片</text>
        <view class="detail-images">
          <image
            v-for="(img, index) in ticket.images"
            :key="index"
            :src="img"
            class="detail-image"
            mode="aspectFill"
            @click="previewImages(ticket.images, index)"
          />
        </view>
      </view>

      <!-- 处理记录 -->
      <view class="detail-section">
        <text class="section-title">处理记录</text>
        <HandleProgress :ticketList="operateComputed" />
      </view>
    </view>
  </BasicContainer>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import BasicContainer from "@/components/common/BasicContainer.vue";
import HandleProgress from "./component/HandleProgress.vue";
import { useTicket } from "@/hooks/useTicket";

const { ticketMap, operateComputed, selectTicketItem } = useTicket();
const ticket = ref<any>({});

// 预览图片
const previewImages = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: images[current],
  });
};

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return "";
  const date = new Date(time);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
    closed: "已关闭",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  const classMap = {
    pending: "status-pending",
    processing: "status-processing",
    completed: "status-completed",
    closed: "status-closed",
  };
  return classMap[status] || "";
};
</script>

<style lang="scss" scoped>
.detail-content {
  padding: 32rpx;

  .detail-section {
    margin-bottom: 32rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
      display: block;
    }

    .detail-row {
      display: flex;
      margin-bottom: 12rpx;

      .detail-label {
        font-size: 28rpx;
        color: #666;
        min-width: 160rpx;
      }

      .detail-value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }

    .problem-description {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      background: #f8f9fa;
      padding: 16rpx;
      border-radius: 8rpx;
    }

    .detail-images {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .detail-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 8rpx;
      }
    }
  }
}
</style>
]]>
