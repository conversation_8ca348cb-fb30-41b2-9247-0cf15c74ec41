import { ref, Ref, computed } from "vue";
import { onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app";
import { useLoginStore } from "../stores/loginStore";
import { storeToRefs } from "pinia";
export function useQueryList() {
  const loginStore = useLoginStore();
  const { factoryId } = storeToRefs(loginStore);
  /** page */
  const page = ref({ current: 1, size: 10, factoryId: factoryId.value });
  /** 传入的参数 */
  const paramsData = ref({});
  /** 是否还有更多数据 */
  const hasMore = ref(true);
  /** 是否正在加载 */
  const loading = ref(false);
  /** 列表数据 */
  const data = ref<any[]>([]);
  /** 查询函数 */
  const func = ref<Function | null>(null);
  /** 防抖定时器 */
  let timer: number | null = null;
  /** 合并后的参数 */
  const mergeParams = computed(() => {
    return { ...page.value, ...paramsData.value };
  });
  // 下拉刷新
  onPullDownRefresh(() => {
    if (loading.value) return;
    page.value.current = 1;
    runFunction(mergeParams.value, true);
    uni.stopPullDownRefresh();
  });

  // 上拉加载更多
  onReachBottom(() => {
    if (loading.value || !hasMore.value) return;
    // 防抖处理
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      page.value.current++;
      runFunction(mergeParams.value, false);
    }, 300);
  });

  /**
   * 初始化查询
   * @param  params - 查询参数
   * @param  callback - 查询回调函数，接收分页参数，返回查询结果
   */
  const fetchQueryList = async (params: any, callback: Function) => {
    page.value.current = 1;
    data.value = [];
    func.value = callback;
    paramsData.value = params;
    await runFunction(mergeParams.value, true);
  };

  /**
   * 执行查询函数
   * @param  params - 分页参数
   * @param  isRefresh - 是否为刷新操作
   */
  const runFunction = async (
    params: { current: number; size: number; [key: string]: any },
    isRefresh?: boolean
  ) => {
    if (!func.value) return;
    loading.value = isRefresh ?? false;
    try {
      const res = await func.value(params);
      if (isRefresh) {
        data.value = res.records;
      } else {
        data.value.push(...res.records);
      }
      hasMore.value = res.records.length === page.value.size;
    } catch (error) {
      console.log(error, "error");
    } finally {
      loading.value = false;
    }
  };

  return { data, hasMore, loading, fetchQueryList };
}
