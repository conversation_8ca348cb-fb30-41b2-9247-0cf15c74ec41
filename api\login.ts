import { request } from "../utils/request";
export function bindUser(query: any) {
  const obj = { state: "MINI" };
  return request({
    url: "/admin/social/bind",
    method: "POST",
    params: { ...obj, ...query },
    data: {},
  });
}
export function vatliUserName(userName: string) {
  return request({
    url: `/admin/user/getTenantId/${userName}`,
    method: "GET",
  });
}
export function login(query: any) {
  const params = { grant_type: "password", randomStr: "blockPuzzle", code: "" };
  return request({
    url: "/auth/oauth/token",
    method: "POST",
    header: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: "Basic cGlnOnBpZw==",
    },
    params: params,
    data: query,
  });
}

export function getUserInfoCode(code: string) {
  const params = { grant_type: "mobile", mobile: `MINI@${code}` };
  return request({
    url: "/auth/mobile/token/social",
    method: "POST",
    params,
    header: {
      Authorization: "Basic cGlnOnBpZw==",
    },
  });
}

export function logout() {
  return request({
    url: "/auth/token/logout",
    method: "DELETE",
    data: {},
  });
}
