<template>
  <view class="login-container">
    <view class="background-decoration">
      <view class="circle circle-1"></view>
      <view class="circle circle-2"></view>
      <view class="circle circle-3"></view>
    </view>

    <view class="content-wrapper">
      <view class="header-section">
        <text class="welcome-title">欢迎登录</text>
      </view>

      <view class="form-card">
        <view class="input-item">
          <view class="input-wrapper">
            <view class="input-icon">👤</view>
            <input
              class="input-content"
              type="text"
              v-model="form.username"
              placeholder="请输入用户名"
            />
          </view>
        </view>

        <view class="input-item">
          <view class="input-wrapper">
            <view class="input-icon">🔒</view>
            <input
              class="input-content"
              type="text"
              v-model="form.password"
              placeholder="请输入密码"
              :password="true"
            />
          </view>
        </view>

        <button class="login-btn" @click="handleLogin">
          <text class="btn-text">登录</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive } from "vue";
import { useLoginStore } from "@/stores/loginStore";

const loginStore = useLoginStore();

// 登录表单
const form = reactive({
  username: "",
  password: "",
});

// 账号密码登录
const handleLogin = async () => {
  if (!form.username || !form.password) {
    uni.showToast({ title: "用户名和密码不能为空", icon: "none" });
    return;
  }

  try {
    await loginStore.loginByUsername(form);
    uni.showToast({
      title: "登录成功",
      icon: "success",
    });
    // 登录成功后返回上一页
    setTimeout(() => {
      uni.switchTab({
        url: "/pages/tabs/home/<USER>",
      });
    }, 1000);
  } catch (error) {
    uni.showToast({
      title: "登录失败，请检查用户名和密码",
      icon: "none",
    });
  }
};
</script>

<style>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.content-wrapper {
  position: relative;
  z-index: 1;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  justify-content: center;
}

.header-section {
  text-align: center;
  margin-bottom: 20rpx;
}

.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-item {
  margin-bottom: 40rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  background: white;
}

.input-icon {
  font-size: 32rpx;
  padding: 0 20rpx;
  color: #666;
  min-width: 80rpx;
  text-align: center;
}

.input-content {
  flex: 1;
  height: 88rpx;
  font-size: 30rpx;
  color: #333;
  background: transparent;
  border: none;
  padding: 0 20rpx 0 0;
}

.input-content::placeholder {
  color: #999;
}

.login-btn {
  margin-top: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  height: 88rpx;
  font-size: 32rpx;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.login-btn:active::before {
  left: 100%;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.btn-text {
  font-weight: 600;
  letter-spacing: 2rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .content-wrapper {
    padding: 60rpx 30rpx;
  }

  .form-card {
    padding: 50rpx 30rpx;
  }

  .welcome-title {
    font-size: 42rpx;
  }
}
</style>
