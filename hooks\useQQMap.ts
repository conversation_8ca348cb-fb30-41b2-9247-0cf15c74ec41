import QQMapWX from "@/utils/qqmap-wx-jssdk.min.js";
export function useQQMap() {
  const qqmapsdk = new QQMapWX({
    key: "W2DBZ-SHTH4-JXPUQ-KRAP2-62RAT-PHF3U",
  });

  /**
   * 地址转换
   * @param address 经纬度
   * @returns 转换后的地址
   */
  const convertAddress = (address: {
    latitude: number;
    longitude: number;
  }): Promise<string> => {
    return new Promise((resolve, reject) => {
      const { latitude, longitude } = address;
      qqmapsdk.reverseGeocoder({
        location: { latitude, longitude },
        success: (res: any) => {
          let { address, formatted_addresses } = res.result;
          resolve(address + formatted_addresses.recommend);
        },
        fail: function (error: any) {
          reject(error);
        },
      });
    });
  };

  return { qqmapsdk, convertAddress };
}
