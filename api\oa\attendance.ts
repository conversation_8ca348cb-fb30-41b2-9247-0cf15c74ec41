import { request } from "../../utils/request";

export function fetchList(query: any) {
  return request({
    url: "/oa/attendance/page",
    method: "GET",
    params: query,
  });
}

export function addObj(obj: any) {
  return request({
    url: "/oa/attendance",
    method: "POST",
    data: obj,
  });
}
export function getObj(id: any) {
  return request({
    url: "/oa/attendance/" + id,
    method: "GET",
  });
}
export function putObj(obj: any) {
  return request({
    url: "/oa/attendance",
    method: "PUT",
    data: obj,
  });
}
export function getAttendanceList(query: any) {
  return request({
    url: "/oa/attendance/list",
    method: "GET",
    params: query,
  });
}
export function clockIn(data: any) {
  return request({
    url: "/oa/attendance/post",
    method: "POST",
    data,
  });
}
