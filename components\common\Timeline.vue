<template>
  <view class="timeline-container">
    <view
      v-for="(item, index) in list"
      :key="item.id || index"
      class="timeline-item"
    >
      <!-- 时间线节点和线 -->
      <view class="timeline-axis">
        <view class="timeline-dot"></view>
        <view v-if="index !== list.length - 1" class="timeline-line"></view>
      </view>
      <!-- 节点内容插槽 -->
      <view class="timeline-content">
        <slot :item="item" :index="index"></slot>
      </view>
    </view>
    <view v-if="!list || list.length === 0" class="timeline-empty">
      <slot name="empty">暂无数据</slot>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  list: any[];
}>();
</script>

<style lang="scss" scoped>
.timeline-container {
  padding: 16rpx 0;
}
.timeline-item {
  display: flex;
  margin-bottom: 8px;
  min-height: 120rpx;
  position: relative;
  &:last-child {
    margin-bottom: 0;
    .timeline-line {
      display: none;
    }
  }
}
.timeline-axis {
  position: relative;
  margin-right: 24rpx;
  width: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.timeline-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #007aff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  z-index: 2;
  position: relative;
  flex-shrink: 0;
}
.timeline-line {
  position: absolute;
  left: 50%;
  top: 20rpx;
  width: 2rpx;
  height: calc(100% + 12rpx);
  background: #e8e8e8;
  transform: translateX(-50%);
}
.timeline-content {
  flex: 1;
}
.timeline-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx dashed #ddd;
  color: #999;
  font-size: 28rpx;
}
</style>
