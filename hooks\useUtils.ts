import { useLoginStore } from "../stores/loginStore";
import { storeToRefs } from "pinia";
export function useUtils() {
  const login = useLoginStore();
  const { accessToken } = storeToRefs(login);

  /**
   * 将临时路径转换为文件并上传图片
   */
  const takePhotoAsFile = (tempFilePath: string): Promise<string> => {
    const url = "https://a.atrace.cn/admin/sys-file/upload";
    const header = {
      Authorization: `Bearer ${accessToken.value}`,
      "TENANT-ID": 2,
    };
    const params = { url, filePath: tempFilePath, name: "file", header };
    return new Promise(async (resolve, reject) => {
      const res = await uni.uploadFile(params);
      if (res.statusCode === 200) {
        const { data } = JSON.parse(res.data);
        resolve(data.url);
      } else {
        const data = JSON.parse(res.data);
        reject(data);
      }
    });
  };
  /**
   * 预览图片
   * @param current 当前图片URL
   * @param urls 所有图片URL数组
   */
  const previewImage = (current: string, urls: string[]) => {
    uni.previewImage({ current, urls });
  };

  return { takePhotoAsFile, previewImage }; // 返回新的函数
}
