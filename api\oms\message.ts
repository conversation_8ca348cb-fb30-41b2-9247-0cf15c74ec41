import { request } from "../../utils/request";
export function fetchList(query: any) {
  return request({
    url: "/oms/omsmessage/page",
    method: "GET",
    params: query,
  });
}

export function addObj(obj: any) {
  return request({
    url: "/oms/omsmessage",
    method: "POST",
    data: obj,
  });
}

export function getObj(id: any) {
  return request({
    url: "/oms/omsmessage/" + id,
    method: "GET",
  });
}

export function delObj(id: any) {
  return request({
    url: "/oms/omsmessage/" + id,
    method: "DELETE",
  });
}

export function putObj(obj: any) {
  return request({
    url: "/oms/omsmessage",
    method: "PUT",
    data: obj,
  });
}
