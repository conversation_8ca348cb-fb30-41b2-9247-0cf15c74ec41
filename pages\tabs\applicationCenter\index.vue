<template>
  <BasicContainer :showFooter="false">
    <!-- 订单中心 -->
    <Card class="section-card" title="工厂中心">
      <view class="grid">
        <view
          class="grid-item"
          v-for="(item, index) in orderCenterItems"
          :key="index"
          @click="navigateToPage(item.route)"
        >
          <view class="item-content">
            <view class="icon-wrapper blue">
              <tn-icon :name="item.icon" :size="iconSize" :color="iconColor" />
            </view>
            <text class="item-name">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </Card>

    <!-- 办公中心 -->
    <Card class="section-card" title="办公中心">
      <view class="grid">
        <view
          class="grid-item"
          v-for="(item, index) in officeCenterItems"
          :key="index"
          @click="navigateToPage(item.route)"
        >
          <view class="item-content">
            <view class="icon-wrapper orange">
              <tn-icon :name="item.icon" :size="iconSize" :color="iconColor" />
            </view>
            <text class="item-name">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </Card>

    <!-- 仓库管理 -->
    <!-- <Card class="section-card" title="仓库管理">
      <view class="grid">
        <view
          class="grid-item"
          v-for="(item, index) in warehouseItems"
          :key="index"
          @click="navigateToPage(item.route)"
        >
          <view class="item-content">
            <view class="icon-wrapper purple">
              <tn-icon :name="item.icon" :size="iconSize" :color="iconColor" />
            </view>
            <text class="item-name">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </Card> -->

    <!-- 培训中心 -->
    <!--  <Card class="section-card" title="培训中心">
      <view class="grid">
        <view
          class="grid-item"
          v-for="(item, index) in trainingItems"
          :key="index"
          @click="navigateToPage(item.route)"
        >
          <view class="item-content">
            <view class="icon-wrapper green">
              <tn-icon :name="item.icon" :size="iconSize" :color="iconColor" />
            </view>
            <text class="item-name">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </Card> -->
  </BasicContainer>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useNavigateTo } from "../../../hooks/useNavigate";
import BasicContainer from "../../../components/common/BasicContainer.vue";
import Card from "../../../components/common/Card.vue";

const { navigateTo } = useNavigateTo();

const iconSize = "30px";

const iconColor = "#007aff";

const orderCenterItems = ref([
  { name: "订单管理", route: "order", icon: "tread-fill" },
  { name: "衣物管理", route: "clothing", icon: "like" },
  { name: "工单管理", route: "workOrder", icon: "receipt" },
  { name: "洗前沟通", route: "washBefor", icon: "service-simple" },
  {
    name: "洗后沟通",
    route: "washAfter",
    icon: "service-simple-fill",
  },
  { name: "工厂看板", route: "factoryBoard", icon: "menu-classify" },
]);

const officeCenterItems = ref([
  { name: "考勤", route: "attendance", icon: "menu-classify" },
]);

const warehouseItems = ref([
  { name: "申请物料", route: "requestMaterials", icon: "rocket" },
  { name: "物料盘点", route: "inventoryCheck", icon: "totop-fill" },
]);
const trainingItems = ref([
  { name: "培训", route: "training", icon: "menu-classify" },
  { name: "考试", route: "exam", icon: "menu-classify" },
]);

const navigateToPage = (route: string) => {
  navigateTo(route);
};
</script>

<style lang="scss" scoped>
// 变量定义
$gap: 8px;
$border-radius: 8px;
$text-color: #333;
$shadow-color: rgba(#000, 0.03);

.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $gap;
  padding: 1px 0;
}

.grid-item {
  border-radius: $border-radius;
  text-align: center;
  cursor: pointer;
  background: #fff;
  box-shadow: 0 1px 2px $shadow-color;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  .item-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 6px;
    gap: 4px;
  }

  .icon-wrapper {
    border-radius: $border-radius;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .item-name {
    font-size: 12px;
    font-weight: 500;
    color: $text-color;
    line-height: 1.2;
  }
}
</style>
