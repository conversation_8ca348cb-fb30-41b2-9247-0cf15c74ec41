<template>
  <view class="card" :style="bodyStyle">
    <view class="stats-header" v-if="title">
      <slot name="title">
        <text class="card-title" :style="titleStyle" @click="titleClick">{{
          title
        }}</text>
      </slot>
      <slot name="extra">
        <text class="card-extra" @click="extraClick">{{ extra || "" }}</text>
      </slot>
    </view>
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  extra: {
    type: String,
    default: "",
  },
  titleStyle: {
    type: Object as any,
    default: () => ({}),
  },
  bodyStyle: {
    type: Object as any,
    default: () => ({}),
  },
});
const emit = defineEmits(["extraClick", "titleClick"]);
const extraClick = () => {
  emit("extraClick");
};
const titleClick = () => {
  emit("titleClick");
};
</script>

<style scoped lang="scss">
/* 统计卡片样式 */
.card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 16px;
  box-sizing: border-box;
  // margin: 0 10px 8px 10px;
  margin-bottom: 8px;

  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    padding-bottom: 8px;

    .card-title {
      color: $uni-color-title;
      font-size: $uni-font-size-base;
      font-weight: 600;
    }

    .card-extra {
      color: $uni-color-primary;
      font-size: $uni-font-size-base;
      font-weight: 500;
    }
  }
}
</style>
