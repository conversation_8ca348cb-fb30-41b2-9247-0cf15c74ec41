import { getCurrentInstance } from "vue";

// useOnLaunch.ts
// 该 Hook 用于获取全局属性 $isResolve 和 $onLaunch，通常用于小程序启动时的相关逻辑。
// 返回值：包含 $isResolve 和 $onLaunch 的对象。

/**
 * 获取全局属性 $isResolve 和 $onLaunch。
 * 通常用于小程序启动时，判断是否已初始化及执行启动回调。
 */
export function useOnLoaunch(): { $isResolve: any; $onLaunch: any } {
  let { $isResolve, $onLaunch } = getCurrentInstance()?.appContext.config
    .globalProperties as any;
  return { $isResolve, $onLaunch };
}
