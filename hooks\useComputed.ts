import { computed, ComputedRef } from "vue";

/**
 * 记忆化计算属性工厂
 *
 * 用于根据传入的参数，自动缓存并复用相同参数的计算属性结果，避免重复计算。
 * 适合需要根据参数动态生成 computed 的场景。
 *
 * @param fn 需要记忆化的计算函数
 * @returns 一个带参数的函数，返回对应参数的计算属性
 *
 * @example
 * const getDouble = useComputed((n: number) => n * 2)
 * const double3 = getDouble(3) // 返回一个 computed，double3.value === 6
 *
 * 注意：仅支持可被 JSON.stringify 正确序列化的参数类型。
 * 若参数为对象，需保证属性顺序一致且无循环引用。
 */
export function useComputed<T extends (...args: unknown[]) => unknown>(
  fn: T
): (...args: Parameters<T>) => ComputedRef<ReturnType<T>> {
  type Result = ComputedRef<ReturnType<T>>;
  const map = new Map<string, Result>();

  return function (...args: Parameters<T>): Result {
    // 仅支持可序列化参数
    const key = JSON.stringify(args);
    if (map.has(key)) {
      // 类型断言比非空断言更符合ESLint规则
      return map.get(key) as Result;
    }
    const result = computed(() => fn(...args));
    map.set(key, result as Result);
    return result as Result;
  };
}
