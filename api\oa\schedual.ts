import { request } from "../../utils/request";

export function fetchList(query: any) {
  return request({
    url: "/oa/schedual/page",
    method: "GET",
    params: query,
  });
}

export function addObj(obj: any) {
  return request({
    url: "/oa/schedual",
    method: "POST",
    data: obj,
  });
}

export function getObj(id: any) {
  return request({
    url: "/oa/schedual/" + id,
    method: "GET",
  });
}

export function delObj(id: any) {
  return request({
    url: "/oa/schedual/" + id,
    method: "DELETE",
  });
}

export function putObj(obj: any) {
  return request({
    url: "/oa/schedual",
    method: "PUT",
    data: obj,
  });
}

export function getSchedualList(query: any) {
  return request({
    url: "/oa/schedual/list",
    method: "GET",
    params: query,
  });
}
