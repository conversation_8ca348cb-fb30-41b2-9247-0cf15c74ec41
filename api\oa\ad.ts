import { request } from "../../utils/request";
export function fetchList(query: any) {
  return request({
    url: "/oa/addata/page",
    method: "GET",
    params: query,
  });
}

export function addObj(obj: any) {
  return request({
    url: "/oa/addata",
    method: "POST",
    data: obj,
  });
}

export function getObj(id: any) {
  return request({
    url: "/oa/addata/" + id,
    method: "GET",
  });
}

export function delObj(id: any) {
  return request({
    url: "/oa/addata/" + id,
    method: "DELETE",
  });
}

export function putObj(obj: any) {
  return request({
    url: "/oa/addata",
    method: "PUT",
    data: obj,
  });
}
