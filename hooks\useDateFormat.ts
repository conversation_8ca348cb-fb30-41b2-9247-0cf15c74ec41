export function useDateFormat() {
  /**
   * 兼容 iOS 的日期字符串处理，将输入转换为 Date 对象。
   */
  function parseDate(input: string | number | Date): Date {
    if (input instanceof Date) return input;
    if (typeof input === "number") return new Date(input);
    if (typeof input === "string") {
      // iOS不支持YYYY-MM-DD，需替换为YYYY/MM/DD
      const fixed = input.replace(/-/g, "/").replace("T", " ");
      return new Date(fixed);
    }
    return new Date(input);
  }

  /**
   * 补零函数，将个位数补齐为两位字符串。
   * @param {number} num - 需要补零的数字
   * @returns {string} 补零后的字符串
   */
  function pad(num: number): string {
    return num < 10 ? "0" + num : "" + num;
  }

  /**
   * 格式化日期为指定格式的字符串。
   * @returns {string} 格式化后的日期字符串
   */
  function format(
    date: string | number | Date,
    formatStr = "YYYY-MM-DD HH:mm:ss"
  ): string {
    const d = parseDate(date);
    const map: Record<string, string> = {
      YYYY: d.getFullYear().toString(),
      MM: pad(d.getMonth() + 1),
      DD: pad(d.getDate()),
      HH: pad(d.getHours()),
      mm: pad(d.getMinutes()),
      ss: pad(d.getSeconds()),
    };
    let result = formatStr;
    Object.keys(map).forEach((key) => {
      result = result.replace(new RegExp(key, "g"), map[key]);
    });
    return result;
  }

  /**
   * 获取指定日期所在月份的起止时间字符串。
   * @param {string | number | Date} date - 需要获取范围的日期，可以为字符串、时间戳或 Date 对象
   * @returns {string} 该月的起止时间字符串，格式为 "YYYY-MM-01 00:00:00,YYYY-MM-DD 23:59:59"
   */
  function getMonthRangeString(date: string | number | Date): string {
    const d = parseDate(date);
    const year = d.getFullYear();
    const month = d.getMonth();
    const firstDay = `${year}-${String(month + 1).padStart(
      2,
      "0"
    )}-01 00:00:00`;
    // 获取下月0号即本月最后一天
    const lastDate = new Date(year, month + 1, 0);
    const lastDay = `${year}-${String(month + 1).padStart(2, "0")}-${String(
      lastDate.getDate()
    ).padStart(2, "0")} 23:59:59`;
    return `${firstDay},${lastDay}`;
  }

  /**
   * 计算两个时间点之间的具体天数、小时和分钟。
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 时间差字符串，格式为 "X天Y小时Z分钟"
   */
  function getTimeDiff(
    startTime: string | number | Date,
    endTime?: string | number | Date
  ): string {
    const start = parseDate(startTime);
    const end = endTime ? parseDate(endTime) : new Date();

    // 计算时间差（毫秒）
    const diff = end.getTime() - start.getTime();

    // 转换为天数、小时和分钟
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    // 构建结果字符串
    const parts: string[] = [];
    if (days > 0) parts.push(`${days}天`);
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);

    return parts.join("") || "0分钟";
  }

  return { format, getMonthRangeString, parseDate, getTimeDiff };
}
