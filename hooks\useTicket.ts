import { ref, reactive, computed, Ref } from "vue";
import { fetchList, beforeWashTicket, afterWashTicket } from "@/api/oms/ticket";
import { fetchList as fetchTicketOperate } from "@/api/oms/ticketOperate";
export const useTicket = () => {
  /** 工单列表 */
  const ticketList = ref<any[]>([]);
  const page = reactive({ current: 1, size: 10 });
  /** 选中的工单 */
  const selectTicket = ref<any>({});
  const ticketMap = {
    "01": "洗前沟通",
    "10": "洗后告知",
    "11": "洗后沟通",
    "20": "投诉",
    "30": "售后",
    "50": "个人业务",
    "60": "精洗服务",
    "70": "增值服务",
  };
  const newRecordsList = ref<any[]>([]);
  const recordsList = ref<any[]>([]);
  /** 工单处理记录 */
  const operateComputed = computed(() => {
    return [...recordsList.value, ...newRecordsList.value];
  });
  /**
   * 获取工单列表
   * @param params 查询参数
   * @returns 工单列表
   */
  const fetchTicketList = async (params?: any) => {
    const { data: res } = await fetchList({ ...page, ...params });
    ticketList.value = res.data.records;
    return res.data;
  };
  /**
   * 获取工单处理记录
   * @param params 查询参数
   * @returns 工单处理记录
   */
  const fetchTicketOperateList = async (params?: any) => {
    const page = { current: 1, size: 50 };
    const { data: res } = await fetchTicketOperate({ ...page, ...params });
    newRecordsList.value = res.data.records.map((item: any) => {
      return handleNote(item);
    });
  };
  /**
   * 转换工单数据中的备注信息
   * @param workData 工单数据
   * @returns 工单处理记录
   */
  const handleTicketItemSyncNote = (workData: any) => {
    const { createUsername: operateMan, createTime, syncNote, note } = workData;
    const data = syncNote ? JSON.parse(syncNote) : { details: note };

    let { images = [], details, content } = data;
    details = details || content;
    if (details && typeof details === "string") {
      // 使用正则表达式提取图片
      const imgRegex = /<img[^>]+src="([^">]+)"/g;
      let match: any;
      while ((match = imgRegex.exec(details))) {
        images.push(match[1]);
      }
      // 移除HTML标签获取纯文本
      details = details.replace(/<[^>]+>/g, "").trim() || content || "";
    }
    if (Object.keys(data).length) {
      const obj = { images, details, operateMan, createTime };
      recordsList.value.push(obj);
    }
  };
  /** 转换工单处理记录中的备注信息 */
  const handleNote = (item: any) => {
    const { note: details, operateMan, createTime } = item;
    // 提取图片URL
    const images: any = [];
    const imgRegex = /<img[^>]+src="([^">]+)"/g;
    let match: any;
    while ((match = imgRegex.exec(details))) {
      images.push(match[1]);
    }
    // 获取纯文本内容
    const textContent = details.replace(/<[^>]+>/g, "").trim();
    return { details: textContent, operateMan, createTime, images };
  };
  /**
   * 选择工单
   * @param item 工单数据
   */
  const selectTicketItem = (item: any) => {
    recordsList.value = [];
    newRecordsList.value = [];
    selectTicket.value = item;
    handleTicketItemSyncNote(item);
    fetchTicketOperateList({ ticketId: item.id });
  };
  /**
   * 创建洗前沟通工单
   * @param obj 工单数据
   * @returns 工单数据
   */
  const createWashBeforeTicket = async (obj: any) => {
    const { data: res } = await beforeWashTicket(obj);
    return res.data;
  };
  /**
   * 创建洗后沟通工单
   * @param obj 工单数据
   * @returns 工单数据
   */
  const createWashAfterTicket = async (obj: any) => {
    const { data: res } = await afterWashTicket(obj);
    return res.data;
  };
  return {
    ticketList,
    ticketMap,
    operateComputed,
    selectTicket,
    fetchTicketList,
    selectTicketItem,
    createWashBeforeTicket,
    createWashAfterTicket,
  };
};
