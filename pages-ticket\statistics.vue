<template>
  <BasicContainer>
    <view class="statistics-container">
      <!-- 页面标题 -->
      <view class="page-header">
        <text class="page-title">工单统计</text>
        <view class="time-selector">
          <picker 
            mode="selector" 
            :range="timeRangeOptions" 
            :value="selectedTimeRange"
            @change="onTimeRangeChange"
          >
            <view class="time-picker">
              <text class="time-text">{{ timeRangeOptions[selectedTimeRange] }}</text>
              <text class="time-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 总览卡片 -->
      <view class="overview-cards">
        <view class="overview-card">
          <text class="card-number">{{ statistics.total }}</text>
          <text class="card-label">总工单数</text>
          <text class="card-trend" :class="getTrendClass(statistics.totalTrend)">
            {{ formatTrend(statistics.totalTrend) }}
          </text>
        </view>
        <view class="overview-card">
          <text class="card-number">{{ statistics.pending }}</text>
          <text class="card-label">待处理</text>
          <text class="card-trend" :class="getTrendClass(statistics.pendingTrend)">
            {{ formatTrend(statistics.pendingTrend) }}
          </text>
        </view>
        <view class="overview-card">
          <text class="card-number">{{ statistics.completed }}</text>
          <text class="card-label">已完成</text>
          <text class="card-trend" :class="getTrendClass(statistics.completedTrend)">
            {{ formatTrend(statistics.completedTrend) }}
          </text>
        </view>
        <view class="overview-card">
          <text class="card-number">{{ (statistics.completionRate * 100).toFixed(1) }}%</text>
          <text class="card-label">完成率</text>
          <text class="card-trend" :class="getTrendClass(statistics.completionRateTrend)">
            {{ formatTrend(statistics.completionRateTrend) }}
          </text>
        </view>
      </view>

      <!-- 图表区域 -->
      <view class="charts-section">
        <!-- 状态分布饼图 -->
        <view class="chart-card">
          <text class="chart-title">工单状态分布</text>
          <view class="pie-chart">
            <canvas 
              canvas-id="statusPieChart" 
              class="chart-canvas"
              @touchstart="onChartTouch"
            ></canvas>
          </view>
          <view class="chart-legend">
            <view 
              v-for="(item, index) in statusDistribution" 
              :key="index"
              class="legend-item"
            >
              <view class="legend-color" :style="{ backgroundColor: item.color }"></view>
              <text class="legend-label">{{ item.label }}</text>
              <text class="legend-value">{{ item.value }}</text>
            </view>
          </view>
        </view>

        <!-- 趋势折线图 -->
        <view class="chart-card">
          <text class="chart-title">工单趋势</text>
          <view class="line-chart">
            <canvas 
              canvas-id="trendLineChart" 
              class="chart-canvas"
              @touchstart="onChartTouch"
            ></canvas>
          </view>
        </view>

        <!-- 类型分布柱状图 -->
        <view class="chart-card">
          <text class="chart-title">工单类型分布</text>
          <view class="bar-chart">
            <canvas 
              canvas-id="typeBarChart" 
              class="chart-canvas"
              @touchstart="onChartTouch"
            ></canvas>
          </view>
        </view>
      </view>

      <!-- 详细数据表格 -->
      <view class="data-table-section">
        <text class="section-title">详细数据</text>
        <view class="data-table">
          <view class="table-header">
            <text class="table-cell">类型</text>
            <text class="table-cell">总数</text>
            <text class="table-cell">待处理</text>
            <text class="table-cell">已完成</text>
            <text class="table-cell">完成率</text>
          </view>
          <view 
            v-for="(item, index) in detailData" 
            :key="index"
            class="table-row"
          >
            <text class="table-cell">{{ item.type }}</text>
            <text class="table-cell">{{ item.total }}</text>
            <text class="table-cell">{{ item.pending }}</text>
            <text class="table-cell">{{ item.completed }}</text>
            <text class="table-cell">{{ (item.completionRate * 100).toFixed(1) }}%</text>
          </view>
        </view>
      </view>

      <!-- 导出按钮 -->
      <view class="export-section">
        <button class="export-btn" @click="exportData">
          <text class="export-icon">📊</text>
          <text class="export-text">导出报表</text>
        </button>
      </view>
    </view>
  </BasicContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import BasicContainer from "@/components/common/BasicContainer.vue";
import { fetchList } from "@/api/oms/ticket";

// 响应式数据
const selectedTimeRange = ref(0);
const loading = ref(false);

// 时间范围选项
const timeRangeOptions = [
  '最近7天',
  '最近30天',
  '最近3个月',
  '最近6个月',
  '最近1年'
];

// 统计数据
const statistics = reactive({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0,
  closed: 0,
  completionRate: 0,
  totalTrend: 0,
  pendingTrend: 0,
  completedTrend: 0,
  completionRateTrend: 0,
});

// 状态分布数据
const statusDistribution = computed(() => [
  { label: '待处理', value: statistics.pending, color: '#ff9800' },
  { label: '处理中', value: statistics.processing, color: '#2196f3' },
  { label: '已完成', value: statistics.completed, color: '#4caf50' },
  { label: '已关闭', value: statistics.closed, color: '#9e9e9e' },
]);

// 详细数据
const detailData = ref<any[]>([]);

// 趋势数据
const trendData = ref<any[]>([]);

// 类型数据
const typeData = ref<any[]>([]);

// 获取统计数据
const fetchStatistics = async () => {
  loading.value = true;
  try {
    const timeRange = getTimeRange();
    const { data: res } = await fetchList({
      startDate: timeRange.start,
      endDate: timeRange.end,
      current: 1,
      size: 1000, // 获取足够多的数据用于统计
    });

    if (res.code === 0) {
      const tickets = res.data.records || [];
      calculateStatistics(tickets);
      generateDetailData(tickets);
      generateTrendData(tickets);
      generateTypeData(tickets);
      
      // 绘制图表
      setTimeout(() => {
        drawPieChart();
        drawLineChart();
        drawBarChart();
      }, 100);
    }
  } catch (error) {
    console.error('获取统计数据失败', error);
    uni.showToast({
      title: '获取统计数据失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 获取时间范围
const getTimeRange = () => {
  const now = new Date();
  const end = now.toISOString().split('T')[0];
  let start = new Date();
  
  switch (selectedTimeRange.value) {
    case 0: // 最近7天
      start.setDate(now.getDate() - 7);
      break;
    case 1: // 最近30天
      start.setDate(now.getDate() - 30);
      break;
    case 2: // 最近3个月
      start.setMonth(now.getMonth() - 3);
      break;
    case 3: // 最近6个月
      start.setMonth(now.getMonth() - 6);
      break;
    case 4: // 最近1年
      start.setFullYear(now.getFullYear() - 1);
      break;
  }
  
  return {
    start: start.toISOString().split('T')[0],
    end
  };
};

// 计算统计数据
const calculateStatistics = (tickets: any[]) => {
  statistics.total = tickets.length;
  statistics.pending = tickets.filter(t => t.status === 'pending').length;
  statistics.processing = tickets.filter(t => t.status === 'processing').length;
  statistics.completed = tickets.filter(t => t.status === 'completed').length;
  statistics.closed = tickets.filter(t => t.status === 'closed').length;
  statistics.completionRate = statistics.total > 0 ? 
    (statistics.completed + statistics.closed) / statistics.total : 0;
  
  // 模拟趋势数据（实际项目中应该从后端获取对比数据）
  statistics.totalTrend = Math.random() * 20 - 10;
  statistics.pendingTrend = Math.random() * 20 - 10;
  statistics.completedTrend = Math.random() * 20 - 10;
  statistics.completionRateTrend = Math.random() * 10 - 5;
};

// 生成详细数据
const generateDetailData = (tickets: any[]) => {
  const typeMap = {
    "01": "洗前沟通",
    "10": "洗后告知",
    "11": "洗后沟通",
    "20": "投诉",
    "30": "售后",
    "50": "个人业务",
    "60": "精洗服务",
    "70": "增值服务",
  };
  
  const typeStats: Record<string, any> = {};
  
  Object.keys(typeMap).forEach(key => {
    typeStats[key] = {
      type: typeMap[key],
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      closed: 0,
      completionRate: 0,
    };
  });
  
  tickets.forEach(ticket => {
    const type = ticket.type;
    if (typeStats[type]) {
      typeStats[type].total++;
      if (ticket.status === 'pending') typeStats[type].pending++;
      else if (ticket.status === 'processing') typeStats[type].processing++;
      else if (ticket.status === 'completed') typeStats[type].completed++;
      else if (ticket.status === 'closed') typeStats[type].closed++;
    }
  });
  
  Object.keys(typeStats).forEach(key => {
    const stats = typeStats[key];
    stats.completionRate = stats.total > 0 ? 
      (stats.completed + stats.closed) / stats.total : 0;
  });
  
  detailData.value = Object.values(typeStats);
};

// 生成趋势数据
const generateTrendData = (tickets: any[]) => {
  // 按日期分组统计
  const dateMap: Record<string, number> = {};
  const timeRange = getTimeRange();
  
  // 初始化日期
  const start = new Date(timeRange.start);
  const end = new Date(timeRange.end);
  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().split('T')[0];
    dateMap[dateStr] = 0;
  }
  
  // 统计每日工单数
  tickets.forEach(ticket => {
    const date = ticket.createTime?.split(' ')[0];
    if (date && dateMap.hasOwnProperty(date)) {
      dateMap[date]++;
    }
  });
  
  trendData.value = Object.entries(dateMap).map(([date, count]) => ({
    date,
    count
  }));
};

// 生成类型数据
const generateTypeData = (tickets: any[]) => {
  const typeMap = {
    "01": "洗前沟通",
    "10": "洗后告知",
    "11": "洗后沟通",
    "20": "投诉",
    "30": "售后",
    "50": "个人业务",
    "60": "精洗服务",
    "70": "增值服务",
  };
  
  const typeCounts: Record<string, number> = {};
  Object.keys(typeMap).forEach(key => {
    typeCounts[key] = 0;
  });
  
  tickets.forEach(ticket => {
    if (typeCounts.hasOwnProperty(ticket.type)) {
      typeCounts[ticket.type]++;
    }
  });
  
  typeData.value = Object.entries(typeCounts).map(([type, count]) => ({
    type: typeMap[type],
    count
  }));
};

// 绘制饼图
const drawPieChart = () => {
  const ctx = uni.createCanvasContext('statusPieChart');
  const centerX = 150;
  const centerY = 150;
  const radius = 100;
  
  let startAngle = 0;
  const total = statistics.total;
  
  if (total === 0) return;
  
  statusDistribution.value.forEach((item, index) => {
    const angle = (item.value / total) * 2 * Math.PI;
    
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, startAngle, startAngle + angle);
    ctx.closePath();
    ctx.setFillStyle(item.color);
    ctx.fill();
    
    startAngle += angle;
  });
  
  ctx.draw();
};

// 绘制折线图
const drawLineChart = () => {
  const ctx = uni.createCanvasContext('trendLineChart');
  const width = 300;
  const height = 200;
  const padding = 40;
  
  if (trendData.value.length === 0) return;
  
  // 绘制坐标轴
  ctx.beginPath();
  ctx.moveTo(padding, height - padding);
  ctx.lineTo(width - padding, height - padding);
  ctx.moveTo(padding, height - padding);
  ctx.lineTo(padding, padding);
  ctx.setStrokeStyle('#ddd');
  ctx.stroke();
  
  // 绘制数据线
  const maxCount = Math.max(...trendData.value.map(d => d.count));
  const stepX = (width - 2 * padding) / (trendData.value.length - 1);
  const stepY = (height - 2 * padding) / maxCount;
  
  ctx.beginPath();
  trendData.value.forEach((item, index) => {
    const x = padding + index * stepX;
    const y = height - padding - item.count * stepY;
    
    if (index === 0) {
      ctx.moveTo(x, y);
    } else {
      ctx.lineTo(x, y);
    }
  });
  ctx.setStrokeStyle('#2196f3');
  ctx.setLineWidth(2);
  ctx.stroke();
  
  ctx.draw();
};

// 绘制柱状图
const drawBarChart = () => {
  const ctx = uni.createCanvasContext('typeBarChart');
  const width = 300;
  const height = 200;
  const padding = 40;
  
  if (typeData.value.length === 0) return;
  
  const maxCount = Math.max(...typeData.value.map(d => d.count));
  const barWidth = (width - 2 * padding) / typeData.value.length * 0.8;
  const stepX = (width - 2 * padding) / typeData.value.length;
  
  typeData.value.forEach((item, index) => {
    const x = padding + index * stepX + stepX * 0.1;
    const barHeight = (item.count / maxCount) * (height - 2 * padding);
    const y = height - padding - barHeight;
    
    ctx.setFillStyle('#4caf50');
    ctx.fillRect(x, y, barWidth, barHeight);
  });
  
  ctx.draw();
};

// 时间范围变化
const onTimeRangeChange = (e: any) => {
  selectedTimeRange.value = e.detail.value;
  fetchStatistics();
};

// 图表触摸事件
const onChartTouch = (e: any) => {
  // 处理图表交互
  console.log('图表触摸事件', e);
};

// 格式化趋势
const formatTrend = (trend: number) => {
  const prefix = trend > 0 ? '+' : '';
  return `${prefix}${trend.toFixed(1)}%`;
};

// 获取趋势样式类
const getTrendClass = (trend: number) => {
  return trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : 'trend-neutral';
};

// 导出数据
const exportData = () => {
  uni.showActionSheet({
    itemList: ['导出Excel', '导出PDF', '分享报表'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          exportExcel();
          break;
        case 1:
          exportPDF();
          break;
        case 2:
          shareReport();
          break;
      }
    }
  });
};

// 导出Excel
const exportExcel = () => {
  uni.showToast({
    title: '导出Excel功能开发中',
    icon: 'none'
  });
};

// 导出PDF
const exportPDF = () => {
  uni.showToast({
    title: '导出PDF功能开发中',
    icon: 'none'
  });
};

// 分享报表
const shareReport = () => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  });
};

// 生命周期
onMounted(() => {
  fetchStatistics();
});
</script>

<style lang="scss" scoped>
.statistics-container {
  padding: 20rpx;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .time-picker {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    
    .time-text {
      font-size: 26rpx;
      color: #333;
    }
    
    .time-arrow {
      font-size: 20rpx;
      color: #999;
    }
  }
}

.overview-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
  
  .overview-card {
    background: #fff;
    padding: 32rpx 24rpx;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    
    .card-number {
      font-size: 48rpx;
      font-weight: bold;
      color: #333;
    }
    
    .card-label {
      font-size: 24rpx;
      color: #666;
    }
    
    .card-trend {
      font-size: 22rpx;
      font-weight: 600;
      
      &.trend-up {
        color: #4caf50;
      }
      
      &.trend-down {
        color: #f44336;
      }
      
      &.trend-neutral {
        color: #999;
      }
    }
  }
}

.charts-section {
  margin-bottom: 30rpx;
}

.chart-card {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .chart-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .chart-canvas {
    width: 100%;
    height: 300rpx;
  }
  
  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-top: 20rpx;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      
      .legend-color {
        width: 24rpx;
        height: 24rpx;
        border-radius: 4rpx;
      }
      
      .legend-label {
        font-size: 24rpx;
        color: #666;
      }
      
      .legend-value {
        font-size: 24rpx;
        color: #333;
        font-weight: 600;
      }
    }
  }
}

.data-table-section {
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .data-table {
    background: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .table-header,
    .table-row {
      display: flex;
      
      .table-cell {
        flex: 1;
        padding: 24rpx 16rpx;
        font-size: 26rpx;
        text-align: center;
        border-right: 1rpx solid #f0f0f0;
        
        &:last-child {
          border-right: none;
        }
      }
    }
    
    .table-header {
      background: #f8f9fa;
      
      .table-cell {
        font-weight: 600;
        color: #333;
      }
    }
    
    .table-row {
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .table-cell {
        color: #666;
      }
    }
  }
}

.export-section {
  .export-btn {
    width: 100%;
    height: 88rpx;
    background: #2196f3;
    color: #fff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    font-size: 32rpx;
    border: none;
    
    .export-icon {
      font-size: 36rpx;
    }
    
    .export-text {
      font-weight: 600;
    }
    
    &:active {
      opacity: 0.8;
    }
  }
}
</style>
