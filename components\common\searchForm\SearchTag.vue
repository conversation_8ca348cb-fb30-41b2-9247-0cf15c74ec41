<template>
  <view class="search-tags">
    <scroll-view
      class="tags-scroll"
      scroll-x="true"
      :show-scrollbar="false"
      enable-flex="true"
    >
      <view class="tags-container">
        <view
          v-for="(item, index) in searchTag"
          :key="item.value"
          class="tag-item"
          :class="{ 'tag-active': tagIndex === index }"
          @click="handleTagClick(item, index)"
        >
          {{ item.label }}
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

/**
 * 搜索标签组件 - 简化版本
 * @props {Array} searchTag - 标签数据数组
 * @props {String} defaultTagIndex - 默认选中的标签索引
 */
const props = defineProps({
  /** 标签数据 */
  searchTag: {
    type: Array as any,
    default: () => [],
  },
  /** 默认选中的标签索引 */
  defaultTagIndex: {
    type: String,
    default: "0",
  },
});

const emit = defineEmits(["tagChange"]);

/** 当前选中的标签索引 */
const tagIndex = ref(Number(props.defaultTagIndex));

/**
 * 标签点击处理
 */
const handleTagClick = (item: any, index: number) => {
  if (tagIndex.value === index) return;
  tagIndex.value = index;
  emit("tagChange", item);
};

// 监听默认索引变化
watch(
  () => props.defaultTagIndex,
  (newVal) => {
    tagIndex.value = Number(newVal);
  }
);
</script>

<style lang="scss" scoped>
.search-tags {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;

  .tags-scroll {
    width: 100%;
    white-space: nowrap;

    .tags-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 8px 0;
      gap: 8px;

      .tag-item {
        flex-shrink: 0;
        padding: 2px 16px;
        margin: 0 4px;
        background-color: #f5f5f5;
        color: #666;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid transparent;

        &:first-child {
          margin-left: 12px;
        }

        &:last-child {
          margin-right: 12px;
        }

        &.tag-active {
          background-color: #007aff;
          color: #fff;
          border-color: #007aff;
        }
      }
    }
  }
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
