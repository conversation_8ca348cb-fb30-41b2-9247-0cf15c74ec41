import { getCurrentInstance } from "vue";

/**
 * 路由跳转及事件通信hook，适用于uni-app页面间eventChannel参数传递
 */
export const useNavigateTo = () => {
  const instance = getCurrentInstance();
  const routerList = {
    // 打卡
    clockIn: "/pages-oa/clockIn/clockIn",
    // 拍照
    takePhotos: "/pages-oa/clockIn/takePhotos",
    // 订单管理
    order: "/pages-oms/order/index",
    // 衣物管理
    clothing: "/pages-oms/clothing/index",
    //订单详情
    orderDetail: "/pages-oms/order/orderDetail",
    // 操作记录
    operatingRecord: "/pages-oms/clothing/operatingRecord",
    //洗前沟通
    washBefor: "/pages-oms/contact/washBefor",
    // 洗后沟通
    washAfter: "/pages-oms/contact/washAfter",
    // 考勤
    attendance: "/pages-oa/attendance/index",
    // 编辑图片
    imageCanvas: "/pages-oms/contact/imageCanvas",
    // 工厂看板
    factoryBoard: "/pages-oms/factoryBoard/index",
    // 超时订单和衣物
    overTimeOrderAndClothe: "/pages-oms/clothing/overTimeOrderAndClothe",
    // 岗位操作
    station: "/pages-post/station/index",
    // 工单管理
    workOrder: "/pages-ticket/index",
  };

  /**
   * 根据路由名称获取实际页面路径
   * @param pathName
   */
  const handlePathMap = (pathName: string) => {
    return routerList[pathName];
  };
  /**
   * 跳转到指定页面并通过eventChannel传递参数
   * @param path 跳转页面路径
   * @param eventName 事件名称
   * @param params 需要传递的参数
   */
  const navigateTo = async (
    path: string,
    eventName?: string,
    params?: any
  ): Promise<void> => {
    uni.navigateTo({
      url: handlePathMap(path),
      success: (res) => {
        if (eventName) {
          res.eventChannel.emit(eventName, params);
        }
      },
    });
  };

  /**
   * 监听eventChannel事件，获取页面跳转时传递的参数
   * @param eventName 事件名称
   * @param callback 回调函数，接收参数
   */
  const onEvent = (
    eventName: string,
    callback: (params: any) => void
  ): void => {
    const eventChannel = (instance?.proxy as any)?.getOpenerEventChannel?.();
    if (eventChannel) {
      eventChannel.on(eventName, callback);
    }
  };
  return { navigateTo, onEvent };
};
