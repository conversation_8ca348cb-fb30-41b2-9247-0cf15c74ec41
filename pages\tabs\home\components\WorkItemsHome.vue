<template>
  <Card title="超时排查">
    <view class="grid-container">
      <view
        v-for="(item, idx) in items"
        :key="idx"
        class="order-count-item"
        :class="item.class"
        @click="handleClick(item)"
      >
        <text>{{ item.label }}</text>
        <text>{{ item.value }}</text>
      </view>
    </view>
  </Card>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, computed } from "vue";
import { useLoginStore } from "../../../../stores/loginStore";
import { useOrder } from "../../../../hooks/useOrder";
import { useClothe } from "../../../../hooks/useClothe";
import { useDateFormat } from "../../../../hooks/useDateFormat";
import { storeToRefs } from "pinia";
import Card from "../../../../components/common/Card.vue";
import { useNavigateTo } from "../../../../hooks/useNavigate";

// 常量定义
const CONSTANTS = {
  STATUS_NOTIN: "40,41,-1,00,01,2,30",
  ORDER_STATUS: "5",
  IS_ACCESSORY: "0",
  IS_URGENT: "1",
  IS_RETURN: "1",
  PAGE_SIZE: 10,
  PAGE_CURRENT: 1,
  DEFAULT_DAY: 3,
  MS_PER_DAY: 8.64e7,
} as const;

// 类型定义
interface DataItem {
  total: number;
  records: any[];
}

interface ItemConfig {
  label: string;
  value: number;
  class: string;
  type: ItemType;
}

type ItemType =
  | "overTimeOrder"
  | "soonOverTimeOrder"
  | "overTimeClothe"
  | "soonOverTimeClothe"
  | "urgentOrder"
  | "returnOrder";

interface QueryParams {
  current: number;
  size: number;
  factoryId: string | number;
  [key: string]: any;
}

// 工具函数
const createDataItem = (): DataItem => ({ total: 0, records: [] });

const updateDataItem = (target: DataItem, source: DataItem): void => {
  Object.assign(target, { total: source.total, records: source.records });
};

/** 获取登录存储 */
const loginStore = useLoginStore();
/** 从登录存储中获取工厂ID */
const { factoryId } = storeToRefs(loginStore);
/** 获取订单数据的方法 */
const { fetchOrderData } = useOrder();
/** 获取衣物数据的方法 */
const { fetchClothesData } = useClothe();
/** 获取日期格式化方法 */
const { format } = useDateFormat();
/** 跳转方法 */
const { navigateTo } = useNavigateTo();

/** 超时订单列表 */
const overTimeOrderList = reactive<DataItem>(createDataItem());
/** 即将超时订单列表 */
const soonOverTimeOrderList = reactive<DataItem>(createDataItem());
/** 超时衣物列表 */
const overTimeClotheList = reactive<DataItem>(createDataItem());
/** 即将超时衣物列表 */
const soonOverTimeClotheList = reactive<DataItem>(createDataItem());
/** 催件订单数据 */
const urgentOrderData = reactive<DataItem>(createDataItem());
/** 返厂订单数据 */
const returnOrderData = reactive<DataItem>(createDataItem());
/** 设置天数 */
const day = ref(CONSTANTS.DEFAULT_DAY);

// 计算属性
const items = computed<ItemConfig[]>(() => [
  {
    label: "超时订单",
    value: overTimeOrderList.total || 0,
    class: "overdue",
    type: "overTimeOrder",
  },
  {
    label: "即将超时订单",
    value: soonOverTimeOrderList.total || 0,
    class: "soon-overdue",
    type: "soonOverTimeOrder",
  },
  {
    label: "超时衣物",
    value: overTimeClotheList.total || 0,
    class: "overdue",
    type: "overTimeClothe",
  },
  {
    label: "即将超时衣物",
    value: soonOverTimeClotheList.total || 0,
    class: "soon-overdue",
    type: "soonOverTimeClothe",
  },
  {
    label: "催件订单",
    value: urgentOrderData.total || 0,
    class: "urgent",
    type: "urgentOrder",
  },
  {
    label: "返厂订单",
    value: returnOrderData.total || 0,
    class: "return",
    type: "returnOrder",
  },
]);

/** 基础参数 */
const basicParams = computed(() => {
  const time = Date.now() - day.value * CONSTANTS.MS_PER_DAY;
  const infactoryTime_le = format(new Date(time));
  const page: QueryParams = {
    current: CONSTANTS.PAGE_CURRENT,
    size: CONSTANTS.PAGE_SIZE,
    factoryId: factoryId.value,
  };

  /** 超时订单参数 */
  const params = { infactoryTime_le, ...page };

  const startTime = Date.now() - day.value * CONSTANTS.MS_PER_DAY;
  const endTime = Date.now() - (day.value - 1) * CONSTANTS.MS_PER_DAY;
  const infactoryTime_between = `${format(new Date(startTime))},${format(
    new Date(endTime)
  )}`;

  /** 即将超时订单参数 */
  const soonParams = { infactoryTime_between, ...page };

  return { params, soonParams, page };
});

/**
 * 获取超时订单数据
 * 查询参数包含 infactoryTime_le、current、size、factoryId 等，数据自动更新到 overTimeOrderList
 */
const overTimeOrder = async (params: QueryParams): Promise<void> => {
  try {
    const form = { status: CONSTANTS.ORDER_STATUS, ...params };
    const res = await fetchOrderData(form);
    updateDataItem(overTimeOrderList, res);
  } catch (error) {
    console.error("获取超时订单数据失败:", error);
  }
};

/**
 * 获取即将超时订单数据
 * 查询参数包含 infactoryTime_between、current、size、factoryId 等，数据自动更新到 soonOverTimeOrderList
 */
const soonOverTimeOrder = async (params: QueryParams): Promise<void> => {
  const form = { status: CONSTANTS.ORDER_STATUS, ...params };
  const res = await fetchOrderData(form);
  updateDataItem(soonOverTimeOrderList, res);
};

/**
 * 获取超时衣物数据
 * 查询参数包含 infactoryTime_le、current、size、factoryId 等，数据自动更新到 overTimeClotheList
 */
const getOverTimeClothe = async (params: QueryParams): Promise<void> => {
  try {
    const form = {
      ...params,
      status_notin: CONSTANTS.STATUS_NOTIN,
      isAccessory: CONSTANTS.IS_ACCESSORY,
    };
    const res = await fetchClothesData(form);
    updateDataItem(overTimeClotheList, res);
  } catch (error) {
    console.error("获取超时衣物数据失败:", error);
  }
};

/**
 * 获取即将超时衣物数据
 * 查询参数包含 infactoryTime_between、current、size、factoryId 等，数据自动更新到 soonOverTimeClotheList
 */
const getSoonOverTimeClothe = async (params: QueryParams): Promise<void> => {
  try {
    const form = {
      ...params,
      status_notin: CONSTANTS.STATUS_NOTIN,
      isAccessory: CONSTANTS.IS_ACCESSORY,
    };
    const res = await fetchClothesData(form);
    updateDataItem(soonOverTimeClotheList, res);
  } catch (error) {
    console.error("获取即将超时衣物数据失败:", error);
  }
};

/**
 * 获取催件订单数据
 * 查询参数包含 current、size、factoryId 等，自动添加 isUrgent: "1"
 */
const getUrgentOrder = async (params: QueryParams): Promise<void> => {
  try {
    const form = {
      ...params,
      status: CONSTANTS.ORDER_STATUS,
      isUrgent: CONSTANTS.IS_URGENT,
    };
    const res = await fetchOrderData(form);
    updateDataItem(urgentOrderData, res);
  } catch (error) {
    console.error("获取催件订单数据失败:", error);
  }
};

/**
 * 获取返厂订单数据
 * 查询参数包含 current、size、factoryId 等，自动添加 isReturn: "1"
 */
const getReturnOrder = async (params: QueryParams): Promise<void> => {
  try {
    const form = {
      ...params,
      status: CONSTANTS.ORDER_STATUS,
      isReturn: CONSTANTS.IS_RETURN,
    };
    const res = await fetchOrderData(form);
    updateDataItem(returnOrderData, res);
  } catch (error) {
    console.error("获取返厂订单数据失败:", error);
  }
};

/**
 * 获取超时和即将超时订单、衣物及催件订单的数据
 * 会自动更新各统计项的total和records
 * 使用Promise.all并行执行所有请求以提高性能
 */
const getOverTimeData = async (): Promise<void> => {
  try {
    const { params, soonParams, page } = basicParams.value;

    // 并行执行所有数据获取请求
    await Promise.all([
      overTimeOrder(params),
      soonOverTimeOrder(soonParams),
      getOverTimeClothe(params),
      getSoonOverTimeClothe(soonParams),
      getUrgentOrder(page),
      getReturnOrder(page),
    ]);
  } catch (error) {
    console.error("获取超时数据失败:", error);
  }
};

/**
 * 获取查询参数
 * @param type 类型
 * @returns 查询参数
 */
const getParams = (type: ItemType): QueryParams => {
  const { params, soonParams, page } = basicParams.value;

  const paramMap: Record<ItemType, QueryParams> = {
    overTimeOrder: { ...params, status: CONSTANTS.ORDER_STATUS },
    soonOverTimeOrder: { ...soonParams, status: CONSTANTS.ORDER_STATUS },
    overTimeClothe: {
      ...params,
      status_notin: CONSTANTS.STATUS_NOTIN,
      isAccessory: CONSTANTS.IS_ACCESSORY,
    },
    soonOverTimeClothe: {
      ...soonParams,
      status_notin: CONSTANTS.STATUS_NOTIN,
      isAccessory: CONSTANTS.IS_ACCESSORY,
    },
    urgentOrder: {
      ...page,
      status: CONSTANTS.ORDER_STATUS,
      isUrgent: CONSTANTS.IS_URGENT,
    },
    returnOrder: {
      ...page,
      status: CONSTANTS.ORDER_STATUS,
      isReturn: CONSTANTS.IS_RETURN,
    },
  };

  return paramMap[type];
};

/**
 * 点击跳转
 * @param item 点击的项
 */
const handleClick = (item: ItemConfig): void => {
  const { value, type } = item;
  if (value < 1) return;

  const params = getParams(type);
  navigateTo("overTimeOrderAndClothe", "overTime", { params, type });
};

export interface WorkItemsExposed {
  /**
   * 获取超时和即将超时订单、衣物及催件订单的数据
   * @returns {Promise<void>} 返回Promise，数据自动更新到组件状态
   */
  getOverTimeData: () => Promise<void>;
}

defineExpose<WorkItemsExposed>({
  getOverTimeData,
});
</script>

<style lang="scss" scoped>
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5px;
}

.order-count-item {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.03);
  padding: 5px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 44px;
  transition: box-shadow 0.2s;
  box-sizing: border-box;

  text {
    &:first-child {
      font-size: $uni-font-size-sm;
      color: #888;
      margin-bottom: 4px;
      letter-spacing: 0.5px;
    }
    &:last-child {
      font-size: $uni-font-size-lg;
      color: #222;
      font-weight: 700;
      line-height: 1.1;
      letter-spacing: 1px;
      align-self: center;
    }
  }

  &.urgent text:last-child {
    color: #ff3b30;
  }
  &.overdue text:last-child {
    color: #ff3b30;
  }
  &.soon-overdue text:last-child {
    color: #ff9500;
  }
  &.return text:last-child {
    color: #ff3b30;
  }
}
</style>
