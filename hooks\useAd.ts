import { computed, reactive, ref, Ref } from "vue";
import { addObj } from "../api/oa/ad";
import { useSystemInfo } from "../stores/systemStore";
import { storeToRefs } from "pinia";
import { useLoginStore } from "../stores/loginStore";
import { useDateFormat } from "./useDateFormat";
export const useAd = () => {
  const systemInfoStore = useSystemInfo();
  const { system } = storeToRefs(systemInfoStore);
  const loginStore = useLoginStore();
  const { userInfo } = storeToRefs(loginStore);
  const { format } = useDateFormat();
  const videoAd = ref<any>(null);
  /* 积分 */
  const score: Ref<number> = ref(uni.getStorageSync("score") || 0);
  /** 默认扣除积分 */
  const scoreParam = ref(50);
  /* 每日广告观看次数 */
  const adWatchCount = ref(30);
  /* 基础参数 */
  const baseParams = reactive({ phoneModel: "", clientVersion: "" });
  /* 广告加载 */
  const adLoad = () => {
    return new Promise((resolve) => {
      if (uni.createRewardedVideoAd && !videoAd.value) {
        //#ifdef MP-WEIXIN
        videoAd.value = uni.createRewardedVideoAd({
          adUnitId: "adunit-6d77fab79f7f4e42",
        });
        //#endif
        //#ifdef APP-PLUS
        videoAd.value = uni.createRewardedVideoAd({
          adpid: "1783232604",
        });
        //#endif
        videoAd.value.onLoad(() => {
          uni.hideLoading();
          console.log("激励视频广告加载成功");
          resolve(true);
        });
        videoAd.value.onError((err: any) => {
          uni.showModal({
            title: "提示",
            content: JSON.stringify(err),
            showCancel: false,
          });
        });
        videoAd.value.onClose(async (res: any) => {
          if (res.isEnded) {
            await uploadAdWatchRecord();
            score.value += 10;
            uni.setStorageSync("score", score.value);
            const today = getTodayString();
            const count = getLocalAdWatchCount.value + 1;
            uni.setStorageSync(`ad_watch_${today}`, count);
            uni.showToast({ title: "获得积分+10", icon: "success" });
          }
        });
      }
    });
  };
  /* 扣除积分 */
  const deductScore = () => {
    score.value -= scoreParam.value;
    uni.setStorageSync("score", score.value);
  };
  /* 获取本地广告观看次数 */
  const getLocalAdWatchCount = computed(() => {
    const today = getTodayString();
    const count = uni.getStorageSync(`ad_watch_${today}`) || 0;
    return count;
  });
  /* 获取今日日期 */
  const getTodayString = () => {
    const today = new Date();
    return format(today, "YYYY-MM-DD");
  };
  /** 观看广告 */
  const watchAd = async () => {
    await getBaseParams();
    try {
      if (getLocalAdWatchCount.value >= adWatchCount.value) {
        uni.showToast({ title: "今日观看次数已达上限", icon: "none" });
        return;
      }
      if (!videoAd.value) {
        uni.showLoading({ title: "加载中..." });
        await adLoad();
      }
      videoAd.value.show();
    } catch (error) {
      uni.hideLoading();
    }
  };
  const uploadAdWatchRecord = async () => {
    //#ifdef MP-WEIXIN
    const params = { ...baseParams, source: "miniapp" };
    await addObj(params);
    //#endif
    //#ifdef APP-PLUS
    const paramsApp = { ...baseParams, source: "app" };
    await addObj(paramsApp);
    //#endif
  };
  /** 获取基础参数 */
  const getBaseParams = async () => {
    const { model, brand } = system.value;
    let clientVersion = "";
    //#ifdef MP-WEIXIN
    const accountInfo = uni.getAccountInfoSync();
    clientVersion = accountInfo.miniProgram.version;
    //#endif
    const { miniOpenid: openId, unionId } = userInfo.value;
    const obj = { phoneModel: model || brand, clientVersion, openId, unionId };
    console.log("obj", obj);
    Object.assign(baseParams, obj);
  };
  return { adLoad, watchAd, score, deductScore };
};
