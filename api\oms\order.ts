import { request } from "../../utils/request";
export function fetchList(query: any) {
  return request({
    url: "/oms/omsorder/page",
    method: "GET",
    params: query,
  });
}
export function addObj(obj: any) {
  return request({
    url: "/oms/omsorder",
    method: "POST",
    data: obj,
  });
}
export function getObj(id: any) {
  return request({
    url: "/oms/omsorder/" + id,
    method: "GET",
  });
}
export function delObj(id: any) {
  return request({
    url: "/oms/omsorder/" + id,
    method: "DELETE",
  });
}
export function putObj(obj: any) {
  return request({
    url: "/oms/omsorder",
    method: "PUT",
    data: obj,
  });
}
export function upload(obj: any) {
  return request({
    url: "/admin/sys-file/upload",
    method: "POST",
    header: {
      "Content-Type": "multipart/form-data",
    },
    data: obj,
  });
}
