import { fetchList, putObj, getBeforProblem } from "../api/oms/orderItem";
import { addObj as addOperating } from "@/api/oms/operating";
import { ref, computed } from "vue";

export function useClothe() {
  const clothesList = ref<any[]>([]);

  // 衣物状态配置
  const clothingStatus = {
    "00": "待入厂",
    "01": "已分拣",
    10: "拆包录检",
    12: "分类",
    20: "洗涤中",
    21: "晾挂",
    22: "烘干",
    23: "初检",
    24: "粘毛去球",
    25: "去渍",
    26: "缝补",
    2701: "单熨烫",
    28: "鞋后整",
    2801: "鞋后整不合格",
    29: "返洗",
    30: "已质检",
    31: "质检不合格",
    32: "已核对",
    40: "已打包",
    41: "已回收",
    "-1": "已删除",
  };

  // 衣物状态背景色配置
  const clothingBackground = {
    "00": "#E8F8F3",
    "01": "#F9F871",
    10: "#9BFB99",
    12: "#9CD2E5",
    20: "#845EC2",
    21: "#09B5D8",
    22: "#F9F871",
    23: "#FFD16E",
    24: "#FFAD8D",
    25: "#FF95B9",
    26: "#F591E0",
    2701: "#A997F3",
    28: "#C2B05E",
    2801: "#C5BA89",
    29: "#F2AC60",
    30: "#FF6700",
    31: "#DE18D6",
    32: "#59C96C",
    40: "#95F204",
    41: "#E36369",
    "-1": "#EB1D38",
  };
  const sourceTypeList = {
    0: "微信小程序",
    1: "支付宝小程序",
    2: "后台订单",
    3: "到位",
    4: "到家",
    5: "美团",
    6: "口碑",
    7: "抖音",
    8: "大鲸洗",
    9: "衣卫士",
    10: "京东",
    11: "丰巢",
    12: "衣优达",
  };
  /**获取衣物数据 */
  const clotheData = computed(() => {
    if (clothesList.value.length > 0) {
      return clothesList.value[0];
    }
    return {};
  });
  /**
   * 获取来源类型名称
   * @param sourceType 来源类型代码
   * @returns 来源类型名称
   */
  const getSourceTypeName = (sourceType: string) => {
    return sourceTypeList[sourceType] ?? "未知来源";
  };
  /**
   * 获取衣物状态名称
   * @param status 衣物状态代码
   * @returns 衣物状态名称
   */
  const getClothingStatusName = (status: string) => {
    return clothingStatus[status] ?? "未知状态";
  };

  /**
   * 获取衣物状态背景色
   * @param status 衣物状态代码
   * @returns 衣物状态背景色
   */
  const getClothingBackground = (status: string) => {
    return clothingBackground[status] || "#AEAEAE";
  };

  /**
   * 获取衣物列表数据
   * @param params 查询参数
   * @returns 衣物列表数据
   */
  const fetchClothesData = async (params: any): Promise<any> => {
    const { data: res } = await fetchList(params);
    clothesList.value = res.data.records;
    return res.data;
  };
  /**
   * 修改衣物信息
   * @param params 修改参数
   * @returns 修改结果
   */
  const updateClothesData = async (params: any): Promise<any> => {
    const { data: res } = await putObj(params);
    return res.data;
  };

  /**
   * 是否继续下一步操作
   * @param status 衣物状态
   * @param postStatus 岗位状态
   * @returns 是否可以继续下一步操作
   */
  const hasIsNext = (status: string, postStatus: string) => {
    if (clothesList.value.length === 0) {
      uni.showToast({ title: "无衣物数据", icon: "none" });
      return false;
    } else if (status === postStatus) {
      uni.showToast({ title: "重复扫码", icon: "none" });
      return false;
    } else if (status === "-1") {
      uni.showToast({ title: "衣物已删除", icon: "none" });
      return false;
    } else if (Number(status) < 10) {
      uni.showToast({ title: "衣物未入厂", icon: "none" });
      return false;
    } else if (!["35", "39"].includes(postStatus) && status === "30") {
      uni.showToast({ title: "衣物已质检", icon: "none" });
      return false;
    } else if (Number(status) >= 40) {
      uni.showToast({ title: "衣物已打包", icon: "none" });
      return false;
    }
    return true;
  };
  /**
   * 获取岗位id
   * @param status 衣物状态
   * @returns 岗位id
   */
  const stationId = function (status: string) {
    let obj = {
      10: "1",
      12: "12",
      19: "13",
      20: "2",
      25: "3",
      26: "4",
      27: "5",
      28: "6",
      29: "7",
      22: "8",
      30: "9",
    };
    return obj[status];
  };
  /**
   * 添加操作记录
   * @param params 操作记录参数
   * @returns 操作记录数据
   */
  const createOperating = async (params: any) => {
    const { data: res } = await addOperating(params);
    return res.data;
  };
  /**
   * 获取沟通问题
   * @param params 查询参数
   * @returns 沟通问题列表
   */
  const getBeforProblemList = async (params: any): Promise<any> => {
    const { data: res } = await getBeforProblem(params);
    const data = JSON.parse(res.data.syncNote);
    return data.data;
  };
  return {
    fetchClothesData,
    getClothingStatusName,
    getClothingBackground,
    updateClothesData,
    clothingStatus,
    clothesList,
    clotheData,
    getBeforProblemList,
    getSourceTypeName,
    hasIsNext,
    stationId,
    createOperating,
  };
}
