<template>
  <Card>
    <view class="operate-camera" @click="handleClockIn">
      <uni-icons type="camera" size="30"></uni-icons>
      <text class="operate-camera-text">打卡</text>
    </view>
  </Card>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useNavigateTo } from "../../../../hooks/useNavigate";
import Card from "../../../../components/common/Card.vue";
const { navigateTo } = useNavigateTo();
const handleClockIn = () => {
  navigateTo("clockIn");
};
</script>

<style lang="scss" scoped>
.operate-camera {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .operate-camera-text {
    font-size: $uni-font-size-base;
    color: $uni-text-color;
    font-weight: bold;
    letter-spacing: 0.1em;
  }
}
</style>
