import { request } from "../../utils/request";

export const fetchList = (query: any) => {
  return request({
    url: "/oms/omsorderitemoperate/page",
    method: "GET",
    params: query,
  });
};
export const addObj = (query: any) => {
  return request({
    url: "/oms/omsorderitemoperate",
    method: "POST",
    data: query,
  });
};
export const putObj = (query: any) => {
  return request({
    url: "/oms/omsorderitemoperate",
    method: "PUT",
    data: query,
  });
};

export const fetchOperating = (query: any) => {
  return request({
    url: "/oms/omsorderitemoperate/stat",
    method: "GET",
    params: query,
  });
};
