<template>
  <view
    class="basic-contriner"
    :style="{ '--bottom-height': bottomHeight + 'px' }"
  >
    <!-- #ifdef APP-PLUS -->
    <view :style="{ height: topHeight + 'px' }"> </view>
    <!-- #endif -->
    <slot></slot>
    <view v-if="showFooter" :style="{ height: bottomHeight + 'px' }"> </view>
  </view>
</template>

<script setup lang="ts">
import { useSystemInfo } from "../../stores/systemStore";
import { storeToRefs } from "pinia";
const props = defineProps({
  showFooter: {
    type: Boolean,
    default: true,
  },
});

const { topHeight, bottomHeight } = storeToRefs(useSystemInfo());
</script>

<style lang="scss" scoped>
.basic-contriner {
  min-height: calc(100vh - var(--bottom-height));
  width: calc(100vw - 20px);
  margin: 0 auto;
  &::after {
    content: "";
    display: block;
    height: 2px;
  }
}
</style>
