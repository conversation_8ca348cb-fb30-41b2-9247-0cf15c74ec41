<template>
  <view class="form-content">
    <tn-form :model="searchForm" ref="formRef" label-position="top">
      <tn-form-item
        v-for="item in columns"
        :key="item.prop"
        :label="item.label"
      >
        <template v-if="item.type === 'daterange'">
          <uni-datetime-picker
            v-model="searchForm[item.prop]"
            type="daterange"
          />
        </template>
        <template v-else-if="item.type === 'select'">
          <uni-data-select
            style="border-radius: 5px"
            v-model="searchForm[item.prop]"
            :localdata="item.dicData"
          ></uni-data-select>
        </template>
        <template v-else="item.type === 'input'">
          <tn-input
            size="sm"
            :type="item.isNumber ? 'number' : 'text'"
            v-model="searchForm[item.prop]"
            :placeholder="item.placeholder"
          />
        </template>
      </tn-form-item>
    </tn-form>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { PropType } from "vue";

/** 表单内容props */
export interface Column {
  label: string;
  type: string;
  prop: string;
  isNumber?: boolean;
  placeholder?: string;
  dicData?: { text: string; value: string; label?: string }[];
}

interface FormContentProps {
  searchForm: any;
  columns: Column[];
}
const props: FormContentProps = defineProps({
  searchForm: {
    type: Object,
    default: () => ({}),
  },
  columns: {
    type: Array as PropType<Column[]>,
    default: () => [],
  },
});

const pickerValue = ref(false);

// 根据选中的value获取对应的label显示文本
const getDisplayLabel = (item: Column, selectedValue: any) => {
  if (!selectedValue || !item.dicData) {
    return "";
  }

  const selectedItem = item.dicData.find(
    (option) => option.value === selectedValue
  );
  return selectedItem ? selectedItem.label : selectedValue;
};
</script>

<style lang="scss" scoped>
.form-content {
  padding: 10px;
}
</style>
