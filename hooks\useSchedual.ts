import { ref, computed, ComputedRef } from "vue";
import { fetchList } from "../api/oa/schedual";
import { useLoginStore } from "../stores/loginStore";
import { useDateFormat } from "./useDateFormat";
import { storeToRefs } from "pinia";

interface SchedualItem {
  day: string;
  [key: string]: any;
}
export function useSchedual() {
  const loginStore = useLoginStore();
  const { factoryId, userInfo } = storeToRefs(loginStore);
  const schedualList = ref<any[]>([]);
  const { format } = useDateFormat();
  const today = format(new Date(), "YYYY-MM-DD");
  const yesterday = format(new Date(Date.now() - 8.64e7), "YYYY-MM-DD");
  const tomorrow = format(new Date(Date.now() + 8.64e7), "YYYY-MM-DD");

  /** 获取排班数据
   * @returns 排班数据
   */
  const fetchSchedual = async () => {
    const { userId } = userInfo.value;
    const page = { current: 1, size: 2, descs: "id" };
    const day_between = `${yesterday},${tomorrow}`;
    const params = { factoryId: factoryId.value, userId, day_between };
    const { data: res } = await fetchList({ ...params, ...page });
    schedualList.value = res.data.records;
  };
  /** 我的排班
   * @returns 我的排班
   */
  const selectSchedual = computed(() => {
    const currentHour = new Date().getHours();
    const targetDay = currentHour < 20 ? today : tomorrow; // 判断当前时间是否超过晚上8点
    const schedualLength = schedualList.value.filter(
      (item) => item.day === targetDay
    );
    return schedualLength;
  });
  return { fetchSchedual, selectSchedual };
}
