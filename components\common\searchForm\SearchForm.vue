<template>
  <view class="search-section">
    <view class="search-relative">
      <view class="search-box-container">
        <tn-search-box
          v-model="searchForm[defaultSearchParams]"
          :placeholder="placeholder"
          :scanButton="scanButton"
          search-button-bg-color="#007aff"
          @scan="scanBtn"
          @search="handleSearch"
        />
      </view>
      <view class="filter-button" @click="isShow = !isShow" v-if="filterButton">
        <uni-icons type="paperplane" size="16" color="#4A8CFF" />
        <text class="filter-text">筛选</text>
      </view>
    </view>
    <tn-popup
      v-model="isShow"
      open-direction="right"
      width="90%"
      radius="0px"
      :popupHeight="popupHeight"
      @close="resetForm"
    >
      <view class="search-popup" :style="{ '--height': `${topHeight}px` }">
        <SearchTag
          :searchTag="tabsData"
          v-if="tabsData.length"
          @tagChange="tagChange"
        />
        <view class="form-container">
          <FormContent :searchForm="popupForm" :columns="formColumns" />
        </view>
        <view
          class="button-container"
          :style="{ 'margin-bottom': `${bottomHeight}px` }"
        >
          <button @click="resetForm" class="reset-button">重置</button>
          <button @click="submit" class="search-button">查询</button>
        </view>
      </view>
    </tn-popup>
  </view>
</template>

<script setup lang="ts">
/**
 * 搜索表单组件
 *
 * @props searchForm - 表单数据对象
 * @props defaultSearchParams - 默认搜索参数的键名
 * @props tabsData - 标签数据
 * @props defaultTagIndex - 默认标签索引
 * @props tabsChangeKey - 标签改变存储的字段
 * @props placeholder - 输入框的占位符
 */
import { ref, reactive, computed, PropType } from "vue";
import FormContent, { Column } from "./FormContent.vue";
import SearchTag from "./SearchTag.vue";
import { useSystemInfo } from "../../../stores/systemStore";
const props: any = defineProps({
  /** 搜索表单 */
  searchForm: {
    type: Object,
    default: () => ({}),
  },
  /** 默认搜索参数 */
  defaultSearchParams: {
    type: String,
    default: "orderSn",
  },
  /** 标签数据 */
  tabsData: {
    type: Array,
    default: () => [],
  },
  /** 默认标签 */
  defaultTagIndex: {
    type: String,
    default: "0",
  },
  /** 标签改变存储的字段 */
  tabsChangeKey: {
    type: String,
    default: "status",
  },
  /** 输入框的占位符 */
  placeholder: {
    type: String,
    default: "请输入订单号",
  },
  /** 扫描按钮 */
  scanButton: {
    type: Boolean,
    default: false,
  },
  /** 表单列 */
  formColumns: {
    type: Array as PropType<Column[]>,
    default: () => [],
  },
  /** 是否显示筛选按钮 */
  filterButton: {
    type: Boolean,
    default: true,
  },
});
const { bottomHeight, topHeight } = useSystemInfo();
const emit = defineEmits(["search"]);
/** 是否禁用按钮 */
const btnDisabled = ref(false);
/** 筛选表单 */
const popupForm = reactive({});
/** 是否显示筛选 */
const isShow = ref(false);
const popupHeight = computed(() => {
  // #ifdef APP-PLUS
  return `calc(100vh - ${topHeight}px)`;
  // #endif
  // #ifdef MP-WEIXIN
  return "100vh";
  // #endif
});

/**
 * 处理搜索操作
 * 如果搜索参数为空，显示提示信息
 */
const handleSearch = () => {
  const key = props.defaultSearchParams;
  if (!props.searchForm[key]) {
    uni.showToast({ title: props.placeholder, icon: "none" });
    return;
  }
  emit("search", { [key]: props.searchForm[key] });
};
/**
 * 提交搜索表单
 * 禁用按钮以防止重复提交
 */
const submit = () => {
  isShow.value = false;
  if (btnDisabled.value) return;
  btnDisabled.value = true;
  handlePopupForm();
  delete props.searchForm[props.defaultSearchParams];
  Object.assign(props.searchForm, popupForm);
  emit("search", props.searchForm);
  handleSearchForm("");
  setTimeout(() => (btnDisabled.value = false), 500);
};
/**
 * 处理筛选表单
 * @description 将筛选表单中的日期范围转换为时间范围
 */
const handlePopupForm = () => {
  const handleKeys = props.formColumns
    .filter((item: Column) => item.type === "daterange")
    .map((item: Column) => item.prop);
  handleKeys.forEach((key: string) => {
    if (popupForm[key] && popupForm[key].length) {
      const [start, end] = popupForm[key];
      popupForm[`${key}_between`] = `${start} 00:00:00,${end} 23:59:59`;
      delete popupForm[key];
    }
  });
};
/**
 * 标签变化处理
 * 更新表单数据中的标签值
 */
const tagChange = (item: any) => {
  const value = item.value === "0" ? "" : item.value;
  popupForm[props.tabsChangeKey] = value;
};
/**
 * 重置表单
 * 清空表单数据
 */
const resetForm = () => {
  Object.keys(popupForm).forEach((key) => {
    popupForm[key] = "";
  });
};
/**
 * 扫描按钮
 */
const scanBtn = async () => {
  const obj = {
    scanType: ["barCode", "qrCode"],
    hideAlbum: true,
    onlyFromCamera: true,
  };
  const res = await uni.scanCode(obj);
  const result = res.result;
  handleSearchForm(result);
  handleSearch();
};
/**
 * 处理搜索表单
 * 更新表单数据中的搜索参数
 */
const handleSearchForm = (value: string) => {
  props.searchForm[props.defaultSearchParams] = value;
};
defineExpose({
  scanBtn,
});
</script>

<style lang="scss" scoped>
.search-section {
  margin-bottom: 8px;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
  border-radius: 5px;
  padding: 5px;
  box-sizing: border-box;

  .search-relative {
    display: flex;
    align-items: center;
    gap: 10px;

    .search-box-container {
      flex: 1;
    }

    .filter-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 5px;
      height: 70rpx;
      background-color: #fff;
      border-radius: 8rpx;

      .filter-text {
        margin-left: 8rpx;
        font-size: 24rpx;
        color: #4a8cff;
      }

      &:active {
        background-color: #eef2f7;
      }
    }
  }

  .search-popup {
    padding: 10px 5px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;
    // #ifdef APP-PLUS
    padding-top: var(--height);
    // #endif
    // #ifdef MP-WEIXIN
    padding-top: 0;
    // #endif
    box-sizing: border-box;
  }

  .form-container {
    flex: 1;
  }

  .button-container {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;

    .reset-button,
    .search-button {
      flex: 1;
      margin: 0 5px;
      padding: 0;
      border: none;
      border-radius: 4px;
      background-color: #4a8cff;
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:active {
        background-color: #357abf;
      }
    }

    .reset-button {
      background: red;
    }
  }
}
</style>
