<template>
  <BasicContainer>
    <view class="batch-process-container">
      <!-- 页面标题 -->
      <view class="page-header">
        <text class="page-title">批量处理工单</text>
        <text class="selected-count">已选择 {{ selectedTickets.length }} 个工单</text>
      </view>

      <!-- 选中的工单列表 -->
      <view class="selected-tickets-section">
        <text class="section-title">选中的工单</text>
        <view class="tickets-list">
          <view 
            v-for="ticket in selectedTickets" 
            :key="ticket.id"
            class="ticket-item"
          >
            <view class="ticket-info">
              <text class="ticket-title">{{ ticketMap[ticket.type] || '未知类型' }}</text>
              <text class="ticket-code">{{ ticket.washCode || ticket.orderSn || ticket.id }}</text>
              <view class="ticket-status" :class="getStatusClass(ticket.status)">
                <text class="status-text">{{ getStatusText(ticket.status) }}</text>
              </view>
            </view>
            <view class="remove-btn" @click="removeTicket(ticket.id)">
              <text class="remove-icon">×</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 批量处理表单 -->
      <view class="batch-form-section">
        <text class="section-title">批量处理设置</text>
        <view class="batch-form">
          <!-- 处理状态 -->
          <view class="form-item">
            <text class="form-label">统一状态</text>
            <view class="status-options">
              <view 
                v-for="(status, key) in statusOptions" 
                :key="key"
                class="status-option"
                :class="{ 'selected': form.status === key }"
                @click="form.status = key"
              >
                <text class="option-text">{{ status }}</text>
              </view>
            </view>
          </view>

          <!-- 统一备注 -->
          <view class="form-item">
            <text class="form-label">统一备注</text>
            <textarea 
              class="form-textarea" 
              v-model="form.note"
              placeholder="请输入统一处理备注（可选）"
              maxlength="500"
            />
            <text class="textarea-counter">{{ form.note.length }}/500</text>
          </view>

          <!-- 个别处理选项 -->
          <view class="form-item">
            <view class="checkbox-wrapper">
              <checkbox 
                :checked="form.individualProcess" 
                @change="toggleIndividualProcess"
                color="#2196f3"
              />
              <text class="checkbox-label">允许个别工单单独处理</text>
            </view>
          </view>

          <!-- 个别处理列表 -->
          <view v-if="form.individualProcess" class="individual-section">
            <text class="individual-title">个别处理设置</text>
            <view 
              v-for="ticket in selectedTickets" 
              :key="ticket.id"
              class="individual-item"
            >
              <view class="individual-header">
                <text class="individual-ticket-title">{{ ticketMap[ticket.type] || '未知类型' }}</text>
                <text class="individual-ticket-code">{{ ticket.washCode || ticket.orderSn || ticket.id }}</text>
              </view>
              <view class="individual-controls">
                <picker 
                  :range="Object.values(statusOptions)" 
                  :value="getStatusIndex(individualSettings[ticket.id]?.status || form.status)"
                  @change="updateIndividualStatus(ticket.id, $event)"
                >
                  <view class="picker-item">
                    <text class="picker-text">{{ getIndividualStatusText(ticket.id) }}</text>
                    <text class="picker-arrow">▼</text>
                  </view>
                </picker>
                <input 
                  class="individual-note" 
                  v-model="individualSettings[ticket.id].note"
                  placeholder="个别备注"
                  maxlength="200"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="cancel-btn" @click="goBack">
          取消
        </button>
        <button class="submit-btn" @click="submitBatchProcess" :disabled="submitting">
          {{ submitting ? '处理中...' : '批量提交' }}
        </button>
      </view>
    </view>
  </BasicContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import BasicContainer from "@/components/common/BasicContainer.vue";
import { useTicket } from "@/hooks/useTicket";
import { addObj } from "@/api/oms/ticketOperate";
import { putObj, getObj } from "@/api/oms/ticket";

const { ticketMap } = useTicket();
const selectedTickets = ref<any[]>([]);
const submitting = ref(false);

// 批量处理表单
const form = reactive({
  status: 'processing',
  note: '',
  individualProcess: false,
});

// 个别处理设置
const individualSettings = reactive<Record<string, any>>({});

// 状态选项
const statusOptions = {
  processing: '处理中',
  completed: '已完成',
  closed: '已关闭',
};

// 获取页面参数
const getPageParams = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  // @ts-ignore
  return currentPage.options || {};
};

// 获取工单详情
const getTicketDetails = async (ids: string[]) => {
  try {
    const promises = ids.map(id => getObj(id));
    const results = await Promise.all(promises);
    selectedTickets.value = results
      .filter(res => res.data.code === 0)
      .map(res => res.data.data);
    
    // 初始化个别处理设置
    selectedTickets.value.forEach(ticket => {
      individualSettings[ticket.id] = {
        status: form.status,
        note: '',
      };
    });
  } catch (error) {
    console.error('获取工单详情失败', error);
    uni.showToast({
      title: '获取工单详情失败',
      icon: 'none'
    });
  }
};

// 移除工单
const removeTicket = (ticketId: string) => {
  const index = selectedTickets.value.findIndex(ticket => ticket.id === ticketId);
  if (index !== -1) {
    selectedTickets.value.splice(index, 1);
    delete individualSettings[ticketId];
  }
  
  if (selectedTickets.value.length === 0) {
    uni.showToast({
      title: '没有选中的工单',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
};

// 切换个别处理模式
const toggleIndividualProcess = (e: any) => {
  form.individualProcess = e.detail.value.length > 0;
};

// 获取状态索引
const getStatusIndex = (status: string) => {
  return Object.keys(statusOptions).indexOf(status);
};

// 更新个别状态
const updateIndividualStatus = (ticketId: string, e: any) => {
  const statusKeys = Object.keys(statusOptions);
  const selectedStatus = statusKeys[e.detail.value];
  individualSettings[ticketId].status = selectedStatus;
};

// 获取个别状态文本
const getIndividualStatusText = (ticketId: string) => {
  const status = individualSettings[ticketId]?.status || form.status;
  return statusOptions[status] || '处理中';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
    closed: "已关闭",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  const classMap = {
    pending: "status-pending",
    processing: "status-processing",
    completed: "status-completed",
    closed: "status-closed",
  };
  return classMap[status] || "";
};

// 批量提交处理
const submitBatchProcess = async () => {
  if (selectedTickets.value.length === 0) {
    uni.showToast({
      title: '没有选中的工单',
      icon: 'none'
    });
    return;
  }

  submitting.value = true;
  try {
    const promises = selectedTickets.value.map(async (ticket) => {
      const settings = form.individualProcess ? individualSettings[ticket.id] : form;
      const finalNote = settings.note || form.note || '批量处理';
      
      // 1. 添加工单操作记录
      const operateData = {
        ticketId: ticket.id,
        note: finalNote,
      };
      await addObj(operateData);

      // 2. 更新工单状态
      const ticketData = {
        ...ticket,
        status: settings.status,
        handler: uni.getStorageSync('userInfo').username || '系统用户',
      };
      await putObj(ticketData);
    });

    await Promise.all(promises);

    uni.showToast({
      title: `成功处理 ${selectedTickets.value.length} 个工单`,
      icon: 'success'
    });

    // 返回上一页并刷新
    setTimeout(() => {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      // @ts-ignore
      if (prevPage && prevPage.$vm && prevPage.$vm.submit) {
        // @ts-ignore
        prevPage.$vm.submit({});
      }
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    console.error('批量处理失败', error);
    uni.showToast({
      title: '批量处理失败',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

onMounted(() => {
  const params = getPageParams();
  if (params.ids) {
    const ids = params.ids.split(',');
    getTicketDetails(ids);
  } else {
    uni.showToast({
      title: '工单ID不能为空',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});
</script>

<style lang="scss" scoped>
.batch-process-container {
  padding: 20rpx;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .selected-count {
    font-size: 28rpx;
    color: #666;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.selected-tickets-section {
  margin-bottom: 30rpx;
}

.tickets-list {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ticket-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .ticket-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    
    .ticket-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
    }
    
    .ticket-code {
      font-size: 26rpx;
      color: #666;
    }
    
    .ticket-status {
      align-self: flex-start;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
      font-size: 22rpx;
      
      .status-text {
        line-height: 1;
      }
      
      &.status-pending {
        background: #fff3cd;
        color: #856404;
      }
      
      &.status-processing {
        background: #d1ecf1;
        color: #0c5460;
      }
      
      &.status-completed {
        background: #d4edda;
        color: #155724;
      }
      
      &.status-closed {
        background: #f8d7da;
        color: #721c24;
      }
    }
  }
  
  .remove-btn {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: #ff4757;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .remove-icon {
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.batch-form,
.individual-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 24rpx;
  
  .form-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }
}

.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  
  .status-option {
    padding: 16rpx 32rpx;
    border-radius: 8rpx;
    background-color: #f5f5f5;
    border: 2rpx solid #f5f5f5;
    
    &.selected {
      background-color: rgba(33, 150, 243, 0.1);
      border-color: #2196f3;
    }
    
    .option-text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.textarea-counter {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 8rpx;
  display: block;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  
  .checkbox-label {
    font-size: 28rpx;
    color: #333;
  }
}

.individual-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.individual-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  
  .individual-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .individual-ticket-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
    
    .individual-ticket-code {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .individual-controls {
    display: flex;
    gap: 16rpx;
    align-items: center;
    
    .picker-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      background-color: #fff;
      border-radius: 6rpx;
      border: 1rpx solid #ddd;
      min-width: 160rpx;
      
      .picker-text {
        font-size: 26rpx;
        color: #333;
      }
      
      .picker-arrow {
        font-size: 20rpx;
        color: #999;
      }
    }
    
    .individual-note {
      flex: 1;
      padding: 12rpx 20rpx;
      background-color: #fff;
      border-radius: 6rpx;
      border: 1rpx solid #ddd;
      font-size: 26rpx;
      color: #333;
    }
  }
}

.submit-section {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  
  &:active {
    opacity: 0.8;
  }
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.submit-btn {
  background-color: #2196f3;
  color: #fff;
  
  &[disabled] {
    background-color: #ccc;
    opacity: 0.6;
  }
}
</style>
