import { request } from "../../utils/request";

export function fetchList(query: any) {
  return request({
    url: "/oms/omsorderitem/page",
    method: "GET",
    params: query,
  });
}

export function addObj(obj: any) {
  return request({
    url: "/oms/omsorderitem",
    method: "POST",
    data: obj,
  });
}
export function putObj(obj: any) {
  return request({
    url: "/oms/omsorderitem",
    method: "PUT",
    data: obj,
  });
}
export function getObj(id: any) {
  return request({
    url: "/oms/omsorderitem/" + id,
    method: "GET",
  });
}

export function delObj(id: any) {
  return request({
    url: "/oms/omsorderitem/" + id,
    method: "DELETE",
  });
}
export function getBeforProblem(query: any) {
  return request({
    url: "/oms/omstickettype/getByName",
    method: "GET",
    params: query,
  });
}
