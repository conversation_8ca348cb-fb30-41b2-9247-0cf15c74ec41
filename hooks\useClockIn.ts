import { ref, reactive, computed } from "vue";
import { useQQMap } from "@/hooks/useQQMap";
import { useUtils } from "@/hooks/useUtils";
import { clockIn } from "@/api/oa/attendance";
export const useClockIn = () => {
  const { convertAddress } = useQQMap();
  const { takePhotoAsFile } = useUtils();
  const locationInfo = reactive({ latitude: 0, longitude: 0 });
  const address = ref("");
  const clockInType = ref("");
  const clockForm = ref({});
  const photoPath = ref("");

  /**获取经纬度 */
  const getLocation = async () => {
    try {
      const res = await uni.getLocation({ type: "wgs84" });
      locationInfo.latitude = res.latitude;
      locationInfo.longitude = res.longitude;
      // 使用 convertAddress 方法获取地址
      address.value = await convertAddress(locationInfo);
    } catch (error) {
      uni.showToast({ title: `${error}`, icon: "none" });
    }
  };
  /**处理打卡数据
   * @param {string} imgUrl 图片路径
   * @returns {Object} 打卡数据
   */
  const handleClockIn = (imgUrl: string) => {
    let params = {};
    if (clockInType.value === "0") {
      const startSelfieUrl = imgUrl;
      const startAddress = address.value;
      params = { ...clockForm.value, startAddress, startSelfieUrl };
    } else if (clockInType.value === "1") {
      const endSelfieUrl = imgUrl;
      const endAddress = address.value;
      params = { ...clockForm.value, endAddress, endSelfieUrl };
    }
    return params;
  };
  /**压缩图片
   * @param {string} imagePath 图片路径
   * @returns {Promise<string>} 压缩后的图片路径
   */
  const compressImage = async (imagePath: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      plus.zip.compressImage(
        {
          src: imagePath,
          dst: imagePath.replace(/\.[^/.]+$/, "_compressed.jpg"), // 生成压缩后的文件名
          quality: 50, // 压缩质量，范围0-100
          width: "50%", // 压缩后的宽度，可以是像素值或百分比
          height: "50%", // 压缩后的高度，可以是像素值或百分比
          overwrite: true, // 是否覆盖目标文件
        },
        (event: any) => {
          console.log("图片压缩成功:", event.target);
          resolve(event.target);
        },
        (error: any) => {
          console.log("图片压缩失败:", error);
          reject(error);
        }
      );
    });
  };

  /**拍照
   * @param {Object} form 表单数据
   * @param {string} type 打卡类型
   * @param {Function} callback 回调函数
   */
  const takePhoto = async (form: any, type: any, callback: any) => {
    setClockForm(form, type);
    await getLocation();
    let compressedPath = "";
    try {
      // APP环境使用原生拍照
      const res: any = await new Promise((resolve, reject) => {
        const cmr = plus.camera.getCamera();
        cmr.captureImage(
          (path: string) => resolve({ tempFilePaths: [path] }),
          (error: any) => reject(error)
        );
      });
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        photoPath.value = res.tempFilePaths[0];

        // 压缩图片
        compressedPath = await compressImage(photoPath.value);

        const imgUrl: string = await takePhotoAsFile(compressedPath);
        const parmas = handleClockIn(imgUrl);
        await clockIn(parmas);
        callback && callback();
        uni.showToast({ title: "打卡成功", icon: "success" });
      }
    } catch (error) {
      console.log("拍照或压缩失败:", error);
    }
  };
  /**设置打卡表单
   * @param {Object} form 表单数据
   * @param {string} type 打卡类型
   */
  const setClockForm = (form: any, type: any) => {
    clockForm.value = form;
    clockInType.value = type;
  };

  return { getLocation, takePhoto };
};
