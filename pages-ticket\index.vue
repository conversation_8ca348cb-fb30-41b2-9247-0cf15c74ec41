<template>
  <BasicContainer>
    <!-- 搜索组件 -->
    <SearchForm
      :searchForm="searchForm"
      defaultSearchParams="washCode"
      placeholder="请输入水洗码或工单号"
      :scanButton="true"
      :tabsData="statusTabs"
      :formColumns="formColumns"
      @search="submit"
    />

    <!-- 批量操作工具栏 -->
    <view class="batch-toolbar" v-if="data.length > 0">
      <view class="selection-info">
        <checkbox-group @change="toggleSelectAll">
          <label class="select-all-label">
            <checkbox :checked="isAllSelected" color="#2196f3" />
            <text class="select-all-text">全选</text>
          </label>
        </checkbox-group>
        <text class="selected-count" v-if="selectedTickets.length > 0"
          >已选择 {{ selectedTickets.length }} 项</text
        >
      </view>
      <view class="batch-actions">
        <button
          class="batch-btn"
          @click="batchProcess"
          v-if="hasProcessPermission && selectedTickets.length > 0"
        >
          <text class="btn-text">批量处理</text>
        </button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-bar" v-if="data.length > 0">
      <view class="stat-item">
        <text class="stat-number">{{ ticketStats.total }}</text>
        <text class="stat-label">总计</text>
      </view>
      <view class="stat-item">
        <text class="stat-number pending">{{ ticketStats.pending }}</text>
        <text class="stat-label">待处理</text>
      </view>
      <view class="stat-item">
        <text class="stat-number processing">{{ ticketStats.processing }}</text>
        <text class="stat-label">处理中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number completed">{{ ticketStats.completed }}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>

    <!-- 工单列表 -->
    <scroll-view
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onScrollToLower"
      :scroll-top="scrollTop"
    >
      <TicketSkeleton :loading="loading" :count="3">
        <view class="ticket-list">
          <view v-for="item in data" :key="item.id" class="ticket-item">
            <view class="ticket-checkbox" v-if="data.length > 0">
              <checkbox
                :checked="isTicketSelected(item.id)"
                @tap.stop="toggleSelectTicket(item.id)"
                color="#2196f3"
              />
            </view>
            <view class="ticket-content">
              <TicketCard
                :title="item.title || ticketMap[item.type] || '未知类型'"
                :ticketData="item"
                :loading="loading"
                @detail="viewDetail"
                @process="processTicket"
              />
            </view>
          </view>
        </view>
      </TicketSkeleton>

      <!-- 加载更多提示 -->
      <view v-if="data.length > 0" class="loading-more">
        {{ hasMore ? "加载中..." : "没有更多数据了" }}
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && data.length === 0" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无工单数据</text>
        <text class="empty-tip">试试调整搜索条件</text>
      </view>
    </scroll-view>

    <!-- 批量处理浮动按钮 -->
    <view
      class="float-action-btn"
      v-if="selectedTickets.length > 0"
      @click="batchProcess"
    >
      <text class="action-btn-text">处理 {{ selectedTickets.length }}</text>
    </view>
  </BasicContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import BasicContainer from "@/components/common/BasicContainer.vue";
import SearchForm from "@/components/common/searchForm/SearchForm.vue";
import TicketCard from "./component/TicketCard.vue";
import TicketSkeleton from "./component/TicketSkeleton.vue";
import { useTicket } from "@/hooks/useTicket";
import { useQueryList } from "@/hooks/useQueryList";
import { useLoginStore } from "@/stores/loginStore";

// 使用工单相关的hooks
const { fetchTicketList, ticketMap } = useTicket();
const { data, hasMore, loading, fetchQueryList } = useQueryList();
const loginStore = useLoginStore();

// 响应式数据
const detailPopup = ref();
const selectedTicket = ref<any>({});
const selectedTickets = ref<string[]>([]);
const scrollTop = ref(0);
const isRefreshing = ref(false);
const lastSearchParams = ref<any>({});

// 缓存机制
const ticketCache = ref<Map<string, any>>(new Map());
const cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存

// 权限控制
const hasProcessPermission = computed(() => {
  // 根据实际权限控制逻辑调整
  return (
    loginStore.userInfo &&
    loginStore.userInfo.roles &&
    (loginStore.userInfo.roles.includes("admin") ||
      loginStore.userInfo.roles.includes("manager"))
  );
});

// 全选状态
const isAllSelected = computed(() => {
  return (
    data.value.length > 0 && selectedTickets.value.length === data.value.length
  );
});

// 统计信息
const ticketStats = computed(() => {
  const stats = {
    total: data.value.length,
    pending: 0,
    processing: 0,
    completed: 0,
    closed: 0,
  };

  data.value.forEach((ticket) => {
    if (ticket.status === "pending") stats.pending++;
    else if (ticket.status === "processing") stats.processing++;
    else if (ticket.status === "completed") stats.completed++;
    else if (ticket.status === "closed") stats.closed++;
  });

  return stats;
});

// 搜索表单
const searchForm = reactive({
  washCode: "",
});

// 状态筛选标签 - 优化标签显示
const statusTabs = ref([
  { label: "全部", value: "0", icon: "all" },
  { label: "待处理", value: "1", icon: "pending", count: 0 },
  { label: "处理中", value: "2", icon: "processing", count: 0 },
  { label: "已完成", value: "3", icon: "completed", count: 0 },
  { label: "已关闭", value: "4", icon: "closed", count: 0 },
]);

// 表单配置
const formColumns = ref([
  { label: "工单号", prop: "ticketNo", type: "input", isNumber: true },
  { label: "水洗码", prop: "washCode", type: "input", isNumber: false },
  { label: "订单号", prop: "orderSn", type: "input", isNumber: true },
  { label: "处理人", prop: "handler", type: "input", isNumber: false },
  {
    label: "创建时间",
    prop: "createTime",
    type: "daterange",
    isNumber: false,
  },
  {
    label: "工单类型",
    prop: "type",
    type: "select",
    options: Object.entries(ticketMap).map(([key, value]) => ({
      label: value,
      value: key,
    })),
    isNumber: false,
  },
]);

// 缓存相关函数
const getCacheKey = (params: any) => {
  return JSON.stringify(params);
};

const getCachedData = (params: any) => {
  const key = getCacheKey(params);
  const cached = ticketCache.value.get(key);
  if (cached && Date.now() - cached.timestamp < cacheExpireTime) {
    return cached.data;
  }
  return null;
};

const setCachedData = (params: any, data: any) => {
  const key = getCacheKey(params);
  ticketCache.value.set(key, {
    data,
    timestamp: Date.now(),
  });
};

// 下拉刷新
const onRefresh = async () => {
  isRefreshing.value = true;
  // 清空缓存
  ticketCache.value.clear();
  // 重新加载数据
  await submit(lastSearchParams.value);
  isRefreshing.value = false;
};

// 滚动到底部加载更多
const onScrollToLower = () => {
  if (hasMore.value && !loading.value) {
    // 加载更多数据
    const currentParams = {
      ...lastSearchParams.value,
      current: Math.floor(data.value.length / 10) + 1,
    };
    fetchQueryList(currentParams, fetchTicketList);
  }
};

// 提交搜索
const submit = async (params: any) => {
  if (loading.value) return;

  lastSearchParams.value = params;

  // 尝试从缓存获取数据
  const cachedData = getCachedData(params);
  if (cachedData && !isRefreshing.value) {
    data.value = cachedData.records || [];
    // 清空选中状态
    selectedTickets.value = [];
    return;
  }

  // 清空选中状态
  selectedTickets.value = [];

  try {
    const result: any = await fetchQueryList(
      { ...params, descs: "id" },
      fetchTicketList
    );
    // 缓存数据
    if (result) {
      setCachedData(params, result);
    }

    // 更新状态标签计数
    updateStatusTabsCount();
  } catch (error) {
    console.error("搜索失败", error);
    uni.showToast({
      title: "搜索失败，请重试",
      icon: "none",
    });
  }
};

// 更新状态标签计数
const updateStatusTabsCount = () => {
  const stats = ticketStats.value;
  statusTabs.value.forEach((tab) => {
    switch (tab.value) {
      case "1":
        tab.count = stats.pending;
        break;
      case "2":
        tab.count = stats.processing;
        break;
      case "3":
        tab.count = stats.completed;
        break;
      case "4":
        tab.count = stats.closed;
        break;
      default:
        tab.count = stats.total;
    }
  });
};

// 查看详情
const viewDetail = (ticket: any) => {
  uni.navigateTo({
    url: `/pages-ticket/ticketDetail?id=${ticket.id}`,
  });
};

// 处理工单
const processTicket = (ticket: any) => {
  uni.navigateTo({
    url: `/pages-ticket/process?id=${ticket.id}`,
  });
};

// 批量处理工单
const batchProcess = () => {
  if (selectedTickets.value.length === 0) {
    uni.showToast({
      title: "请选择要处理的工单",
      icon: "none",
    });
    return;
  }

  if (selectedTickets.value.length === 1) {
    // 如果只选择了一个工单，直接跳转到处理页面
    uni.navigateTo({
      url: `/pages-ticket/process?id=${selectedTickets.value[0]}`,
    });
  } else {
    // 如果选择了多个工单，跳转到批量处理页面
    uni.navigateTo({
      url: `/pages-ticket/batchProcess?ids=${selectedTickets.value.join(",")}`,
    });
  }
};

// 选择/取消选择工单
const toggleSelectTicket = (id: string) => {
  const index = selectedTickets.value.indexOf(id);
  if (index === -1) {
    selectedTickets.value.push(id);
  } else {
    selectedTickets.value.splice(index, 1);
  }
};

// 判断工单是否被选中
const isTicketSelected = (id: string) => {
  return selectedTickets.value.includes(id);
};

// 全选/取消全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedTickets.value = [];
  } else {
    selectedTickets.value = data.value.map((item) => item.id);
  }
};

// 预览图片
const previewImages = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: images[current],
  });
};

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return "";
  const date = new Date(time);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
    closed: "已关闭",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  const classMap = {
    pending: "status-pending",
    processing: "status-processing",
    completed: "status-completed",
    closed: "status-closed",
  };
  return classMap[status] || "";
};

// 生命周期
onMounted(() => {
  submit({});
});
</script>

<style lang="scss" scoped>
.stats-bar {
  display: flex;
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .stat-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;

    .stat-number {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;

      &.pending {
        color: #ff9800;
      }

      &.processing {
        color: #2196f3;
      }

      &.completed {
        color: #4caf50;
      }
    }

    .stat-label {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.scroll-container {
  height: calc(100vh - 300rpx);
  padding: 0 20rpx;
}

.ticket-list {
  padding-bottom: 20rpx;
}

.ticket-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;

  .ticket-checkbox {
    flex-shrink: 0;
  }

  .ticket-content {
    flex: 1;
  }
}

.loading-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 24rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 12rpx;
  }

  .empty-tip {
    font-size: 26rpx;
    color: #999;
  }
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .selection-info {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .select-all-label {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .select-all-text {
        font-size: 28rpx;
        color: #333;
      }
    }

    .selected-count {
      font-size: 26rpx;
      color: #666;
    }
  }

  .batch-actions {
    .batch-btn {
      padding: 12rpx 24rpx;
      background: #2196f3;
      color: #fff;
      border-radius: 24rpx;
      font-size: 26rpx;
      border: none;

      .btn-text {
        line-height: 1;
      }
    }
  }
}

.float-action-btn {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #2196f3;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(33, 150, 243, 0.3);
  z-index: 999;

  .action-btn-text {
    color: #fff;
    font-size: 24rpx;
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
  }

  &:active {
    transform: scale(0.95);
  }
}

.detail-popup {
  width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #eee;

    .popup-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      cursor: pointer;
    }
  }

  .popup-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 32rpx;

    .detail-section {
      margin-bottom: 32rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 16rpx;
        display: block;
      }

      .detail-row {
        display: flex;
        margin-bottom: 12rpx;

        .detail-label {
          font-size: 28rpx;
          color: #666;
          min-width: 160rpx;
        }

        .detail-value {
          font-size: 28rpx;
          color: #333;
          flex: 1;
        }
      }

      .problem-description {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        background: #f8f9fa;
        padding: 16rpx;
        border-radius: 8rpx;
      }

      .detail-images {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .detail-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
}
</style>
