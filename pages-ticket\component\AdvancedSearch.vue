<template>
  <view class="advanced-search">
    <!-- 快速搜索 -->
    <view class="quick-search">
      <view class="search-input-wrapper">
        <input 
          class="search-input"
          v-model="quickSearchText"
          placeholder="搜索工单号、水洗码、订单号"
          @input="onQuickSearch"
          @confirm="onQuickSearchConfirm"
        />
        <view class="search-btn" @click="onQuickSearchConfirm">
          <text class="search-icon">🔍</text>
        </view>
      </view>
      
      <!-- 扫码按钮 -->
      <view class="scan-btn" @click="onScan">
        <text class="scan-icon">📷</text>
      </view>
    </view>

    <!-- 高级筛选 -->
    <view class="advanced-filters" v-if="showAdvanced">
      <!-- 工单类型 -->
      <view class="filter-group">
        <text class="filter-label">工单类型</text>
        <view class="filter-options">
          <view 
            v-for="(type, key) in ticketTypes" 
            :key="key"
            class="filter-option"
            :class="{ 'selected': selectedTypes.includes(key) }"
            @click="toggleType(key)"
          >
            <text class="option-text">{{ type }}</text>
          </view>
        </view>
      </view>

      <!-- 工单状态 -->
      <view class="filter-group">
        <text class="filter-label">工单状态</text>
        <view class="filter-options">
          <view 
            v-for="(status, key) in statusOptions" 
            :key="key"
            class="filter-option"
            :class="{ 'selected': selectedStatuses.includes(key) }"
            @click="toggleStatus(key)"
          >
            <text class="option-text">{{ status }}</text>
          </view>
        </view>
      </view>

      <!-- 时间范围 -->
      <view class="filter-group">
        <text class="filter-label">创建时间</text>
        <view class="date-range">
          <picker 
            mode="date" 
            :value="dateRange.start"
            @change="onStartDateChange"
          >
            <view class="date-picker">
              <text class="date-text">{{ dateRange.start || '开始日期' }}</text>
              <text class="date-arrow">📅</text>
            </view>
          </picker>
          <text class="date-separator">至</text>
          <picker 
            mode="date" 
            :value="dateRange.end"
            @change="onEndDateChange"
          >
            <view class="date-picker">
              <text class="date-text">{{ dateRange.end || '结束日期' }}</text>
              <text class="date-arrow">📅</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 处理人 -->
      <view class="filter-group">
        <text class="filter-label">处理人</text>
        <input 
          class="filter-input"
          v-model="handlerFilter"
          placeholder="输入处理人姓名"
        />
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="toggle-advanced" @click="toggleAdvanced">
        <text class="toggle-text">{{ showAdvanced ? '收起筛选' : '高级筛选' }}</text>
        <text class="toggle-arrow" :class="{ 'rotated': showAdvanced }">▼</text>
      </view>
      
      <view class="filter-actions" v-if="showAdvanced">
        <button class="reset-btn" @click="resetFilters">重置</button>
        <button class="apply-btn" @click="applyFilters">应用筛选</button>
      </view>
    </view>

    <!-- 保存的搜索条件 -->
    <view class="saved-searches" v-if="savedSearches.length > 0">
      <text class="saved-title">常用搜索</text>
      <scroll-view class="saved-list" scroll-x>
        <view 
          v-for="(search, index) in savedSearches" 
          :key="index"
          class="saved-item"
          @click="applySavedSearch(search)"
        >
          <text class="saved-name">{{ search.name }}</text>
          <view class="delete-saved" @click.stop="deleteSavedSearch(index)">
            <text class="delete-icon">×</text>
          </view>
        </view>
        <view class="save-current" @click="saveCurrentSearch">
          <text class="save-icon">+</text>
          <text class="save-text">保存当前</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";

interface Props {
  ticketTypes: Record<string, string>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  search: [params: any];
  scan: [];
}>();

// 响应式数据
const quickSearchText = ref('');
const showAdvanced = ref(false);
const selectedTypes = ref<string[]>([]);
const selectedStatuses = ref<string[]>([]);
const handlerFilter = ref('');
const dateRange = reactive({
  start: '',
  end: '',
});

// 保存的搜索条件
const savedSearches = ref<any[]>([]);

// 状态选项
const statusOptions = {
  pending: '待处理',
  processing: '处理中',
  completed: '已完成',
  closed: '已关闭',
};

// 防抖搜索
let searchTimer: any = null;
const onQuickSearch = () => {
  clearTimeout(searchTimer);
  searchTimer = setTimeout(() => {
    if (quickSearchText.value.trim()) {
      emitSearch();
    }
  }, 500);
};

// 确认搜索
const onQuickSearchConfirm = () => {
  clearTimeout(searchTimer);
  emitSearch();
};

// 扫码
const onScan = () => {
  emit('scan');
};

// 切换工单类型
const toggleType = (type: string) => {
  const index = selectedTypes.value.indexOf(type);
  if (index === -1) {
    selectedTypes.value.push(type);
  } else {
    selectedTypes.value.splice(index, 1);
  }
};

// 切换工单状态
const toggleStatus = (status: string) => {
  const index = selectedStatuses.value.indexOf(status);
  if (index === -1) {
    selectedStatuses.value.push(status);
  } else {
    selectedStatuses.value.splice(index, 1);
  }
};

// 日期选择
const onStartDateChange = (e: any) => {
  dateRange.start = e.detail.value;
};

const onEndDateChange = (e: any) => {
  dateRange.end = e.detail.value;
};

// 切换高级筛选
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value;
};

// 重置筛选
const resetFilters = () => {
  quickSearchText.value = '';
  selectedTypes.value = [];
  selectedStatuses.value = [];
  handlerFilter.value = '';
  dateRange.start = '';
  dateRange.end = '';
  emitSearch();
};

// 应用筛选
const applyFilters = () => {
  emitSearch();
};

// 发送搜索事件
const emitSearch = () => {
  const params: any = {};
  
  if (quickSearchText.value.trim()) {
    params.keyword = quickSearchText.value.trim();
  }
  
  if (selectedTypes.value.length > 0) {
    params.types = selectedTypes.value;
  }
  
  if (selectedStatuses.value.length > 0) {
    params.statuses = selectedStatuses.value;
  }
  
  if (handlerFilter.value.trim()) {
    params.handler = handlerFilter.value.trim();
  }
  
  if (dateRange.start) {
    params.startDate = dateRange.start;
  }
  
  if (dateRange.end) {
    params.endDate = dateRange.end;
  }
  
  emit('search', params);
};

// 保存当前搜索
const saveCurrentSearch = () => {
  uni.showModal({
    title: '保存搜索条件',
    editable: true,
    placeholderText: '请输入搜索条件名称',
    success: (res) => {
      if (res.confirm && res.content) {
        const searchCondition = {
          name: res.content,
          quickSearchText: quickSearchText.value,
          selectedTypes: [...selectedTypes.value],
          selectedStatuses: [...selectedStatuses.value],
          handlerFilter: handlerFilter.value,
          dateRange: { ...dateRange },
        };
        savedSearches.value.push(searchCondition);
        // 保存到本地存储
        uni.setStorageSync('savedTicketSearches', savedSearches.value);
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
      }
    }
  });
};

// 应用保存的搜索
const applySavedSearch = (search: any) => {
  quickSearchText.value = search.quickSearchText;
  selectedTypes.value = [...search.selectedTypes];
  selectedStatuses.value = [...search.selectedStatuses];
  handlerFilter.value = search.handlerFilter;
  dateRange.start = search.dateRange.start;
  dateRange.end = search.dateRange.end;
  emitSearch();
};

// 删除保存的搜索
const deleteSavedSearch = (index: number) => {
  savedSearches.value.splice(index, 1);
  uni.setStorageSync('savedTicketSearches', savedSearches.value);
};

// 初始化
const init = () => {
  // 加载保存的搜索条件
  const saved = uni.getStorageSync('savedTicketSearches');
  if (saved && Array.isArray(saved)) {
    savedSearches.value = saved;
  }
};

// 生命周期
init();
</script>

<style lang="scss" scoped>
.advanced-search {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.quick-search {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  
  .search-input-wrapper {
    flex: 1;
    display: flex;
    background: #f5f5f5;
    border-radius: 24rpx;
    overflow: hidden;
    
    .search-input {
      flex: 1;
      padding: 20rpx 24rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .search-btn {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #2196f3;
      
      .search-icon {
        font-size: 32rpx;
        color: #fff;
      }
    }
  }
  
  .scan-btn {
    width: 80rpx;
    height: 80rpx;
    background: #f5f5f5;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .scan-icon {
      font-size: 32rpx;
    }
  }
}

.advanced-filters {
  margin-bottom: 20rpx;
}

.filter-group {
  margin-bottom: 24rpx;
  
  .filter-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 16rpx;
    display: block;
  }
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  
  .filter-option {
    padding: 12rpx 24rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    border: 2rpx solid transparent;
    
    &.selected {
      background: rgba(33, 150, 243, 0.1);
      border-color: #2196f3;
    }
    
    .option-text {
      font-size: 26rpx;
      color: #333;
    }
  }
}

.date-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .date-picker {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    
    .date-text {
      font-size: 28rpx;
      color: #333;
    }
    
    .date-arrow {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .date-separator {
    font-size: 26rpx;
    color: #666;
  }
}

.filter-input {
  width: 100%;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.action-buttons {
  .toggle-advanced {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx;
    
    .toggle-text {
      font-size: 28rpx;
      color: #2196f3;
    }
    
    .toggle-arrow {
      font-size: 20rpx;
      color: #2196f3;
      transition: transform 0.3s ease;
      
      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
  
  .filter-actions {
    display: flex;
    gap: 20rpx;
    margin-top: 20rpx;
    
    .reset-btn,
    .apply-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      border: none;
      
      &:active {
        opacity: 0.8;
      }
    }
    
    .reset-btn {
      background: #f5f5f5;
      color: #666;
    }
    
    .apply-btn {
      background: #2196f3;
      color: #fff;
    }
  }
}

.saved-searches {
  margin-top: 24rpx;
  
  .saved-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 16rpx;
    display: block;
  }
  
  .saved-list {
    white-space: nowrap;
    
    .saved-item,
    .save-current {
      display: inline-flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      margin-right: 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      position: relative;
    }
    
    .saved-item {
      background: rgba(33, 150, 243, 0.1);
      color: #2196f3;
      
      .saved-name {
        max-width: 120rpx;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .delete-saved {
        width: 32rpx;
        height: 32rpx;
        background: rgba(255, 71, 87, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .delete-icon {
          font-size: 20rpx;
          color: #ff4757;
        }
      }
    }
    
    .save-current {
      background: #f5f5f5;
      color: #666;
      border: 2rpx dashed #ddd;
      
      .save-icon {
        font-size: 28rpx;
      }
      
      .save-text {
        font-size: 24rpx;
      }
    }
  }
}
</style>
