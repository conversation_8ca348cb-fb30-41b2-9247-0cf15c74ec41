import { request } from "@/utils/request";
export function fetchList(query: any) {
  return request({
    url: "/oms/omsticket/page",
    method: "GET",
    params: query,
  });
}

export function addObj(obj: any) {
  return request({
    url: "/oms/omsticket",
    method: "POST",
    data: obj,
  });
}

export function getObj(id: any) {
  return request({
    url: "/oms/omsticket/" + id,
    method: "GET",
  });
}

export function delObj(id: any) {
  return request({
    url: "/oms/omsticket/" + id,
    method: "DELETE",
  });
}

export function putObj(obj: any) {
  return request({
    url: "/oms/omsticket",
    method: "PUT",
    data: obj,
  });
}
export function beforeWashTicket(obj: any) {
  return request({
    url: "/oms/omsticket/beforeWashInfo",
    method: "POST",
    data: obj,
  });
}
export function afterWashTicket(obj: any) {
  return request({
    url: "/oms/omsticket/rearWashInfo",
    method: "POST",
    data: obj,
  });
}
