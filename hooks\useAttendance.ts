import { ref, computed, Ref, ComputedRef } from "vue";
import { getAttendanceList, putObj } from "../api/oa/attendance";
import { useLoginStore } from "../stores/loginStore";
import { storeToRefs } from "pinia";
import { useDateFormat } from "./useDateFormat";
import { useUserDataStore } from "../stores/userDataStore";

type WorkStatus = { label: string; value: string };
type AttendanceStats = {
  totalDays: number;
  totalWorkHours: number;
  actualWorkHours: number;
  averageWorkHours: number;
  totalLeaveHours: number;
};
export function useAttendance() {
  const loginStore = useLoginStore();
  const { factoryId, userInfo } = storeToRefs(loginStore);
  const { format } = useDateFormat();
  const userDataStore = useUserDataStore();
  const { setAttendanceData, getAllAttendanceData } = userDataStore;
  /**
   * 工作状态配置
   * @type {Array<{label: string, value: string}>}
   */
  const workStatus: WorkStatus[] = [
    { label: "休息", value: "2" },
    { label: "培训", value: "3" },
    { label: "吃饭", value: "4" },
    { label: "开会", value: "5" },
  ];

  /**
   * 考勤数据列表
   */
  const attendanceData = ref<any[]>([]);

  /** 本月考勤统计数据
   *  考勤天数、总工时、实际工时、平均工时、离岗时间
   */
  const attendanceStats = computed<AttendanceStats>(() => {
    if (!attendanceData.value.length) {
      return {
        totalDays: 0,
        totalWorkHours: 0,
        actualWorkHours: 0,
        averageWorkHours: 0,
        totalLeaveHours: 0,
      };
    }

    const stats = attendanceData.value.reduce(
      (acc, curr) => {
        // 统计考勤天数（有考勤记录的天数）
        if (curr.data && curr.data.length > 0) {
          acc.totalDays++;
        }

        // 累加工时
        acc.totalWorkHours += curr.workMinutes || 0;
        acc.totalLeaveHours += curr.leaveMinutes || 0;

        return acc;
      },
      { totalDays: 0, totalWorkHours: 0, totalLeaveHours: 0 }
    );

    // 计算实际工时（总工时减去离岗时间）
    const actualWorkHours = Number(
      (stats.totalWorkHours - stats.totalLeaveHours).toFixed(1)
    );

    // 计算平均工时（实际工时除以考勤天数）
    const averageWorkHours =
      stats.totalDays > 0
        ? Number((actualWorkHours / stats.totalDays).toFixed(1))
        : 0;

    return {
      totalDays: stats.totalDays,
      totalWorkHours: Number(stats.totalWorkHours.toFixed(1)),
      actualWorkHours,
      averageWorkHours,
      totalLeaveHours: Number(stats.totalLeaveHours.toFixed(1)),
    };
  });

  /**
   * 获取今日考勤数据的计算属性
   */
  const todayAttendance = computed(() => {
    const today = format(new Date(), "YYYY-MM-DD");
    return attendanceData.value.find((item) => item.date === today) || {};
  });

  /**
   * 异步获取考勤列表数据
   * @param params 查询参数
   * @returns 考勤列表数据
   */
  const fetchAttendanceList = async (params: Record<string, any>) => {
    const { userId } = userInfo.value;
    const paramsObj = { ...params, userId, factoryId: factoryId.value };
    const { data: res } = await getAttendanceList(paramsObj);
    const attendanceList = handleAttendanceList(res.data);
    setAttendanceData(todayAttendance.value);
    return attendanceList;
  };

  /**
   * 处理考勤列表数据，将原始数据转换为数组格式
   * @param data 原始数据
   * @returns 考勤列表数据
   */
  const handleAttendanceList = (data: any) => {
    const attendanceList = ref<any[]>([]);
    Object.entries(data).forEach(([key, value]) => {
      if (value && typeof value === "object" && !Array.isArray(value)) {
        Object.entries(value).forEach(([k, v]) => {
          let dateForm = handleAttendanceTime(v);
          attendanceList.value.push({ date: k, ...dateForm });
        });
      }
    });
    // 按日期升序排序
    attendanceList.value.sort((a, b) => a.date.localeCompare(b.date));
    attendanceData.value = attendanceList.value;
    getAllAttendanceData(attendanceData.value);
    return attendanceList.value;
  };

  /**
   * 获取工作状态标签
   * @param value 工作状态值
   * @returns 工作状态标签
   */
  const getWorkStatus = (value: string) => {
    return workStatus.find((item) => item.value === value)?.label || "";
  };

  /**
   * 处理单天的考勤时间数据，格式化时间并计算总时长
   * @param arr 单天的考勤时间数据
   * @returns 处理后的考勤时间数据
   */
  const handleAttendanceTime = (arr: any) => {
    let workMinutes = 0; // 上班时间
    let leaveMinutes = 0; // 离岗时间
    const data = arr.map((item: any) => {
      item.startTime = format(item.startTime, "HH:mm");
      item.endTime = item.endTime ? format(item.endTime, "HH:mm") : "";
      // 计算每条的时长
      if (item.startTime && item.endTime) {
        const [startHour, startMinute] = item.startTime.split(":").map(Number);
        const [endHour, endMinute] = item.endTime.split(":").map(Number);
        const startTotal = startHour * 60 + startMinute;
        const endTotal = endHour * 60 + endMinute;
        const diff = endTotal - startTotal;
        if (diff > 0) {
          if (item.type === "1") {
            workMinutes += diff;
          } else {
            leaveMinutes += diff;
          }
        }
      }
      return item;
    });
    return {
      /* 上班时间 */
      workMinutes: Number((workMinutes / 60).toFixed(1)),
      /* 离岗时间 */
      leaveMinutes: Number((leaveMinutes / 60).toFixed(1)),
      /* 考勤数据 */
      data,
    };
  };
  /**
   * 申请补卡
   * @param data 补卡数据
   */
  const editApplyForLeave = async (data: any) => {
    try {
      await putObj(data);
    } catch (error) {
      console.log(error);
    }
  };

  return {
    fetchAttendanceList,
    handleAttendanceTime,
    getWorkStatus,
    editApplyForLeave,
    attendanceData,
    attendanceStats,
    todayAttendance,
  }; // 返回考勤列表和获取方法，供组件使用
}
