import { ref } from "vue";
import { fetchList, getObj } from "../api/oms/order";
import { useLoginStore } from "../stores/loginStore";
import { storeToRefs } from "pinia";

export function useOrder() {
  const { factoryId } = storeToRefs(useLoginStore());
  const orderList = ref([]);
  const orderStatus = [
    { label: "全部", value: "" },
    { label: "待付款", value: "0" },
    { label: "待上门", value: "1" },
    { label: "已下单", value: "2" },
    { label: "已取件", value: "3" },
    { label: "已签收", value: "4" },
    { label: "洗涤中", value: "5" },
    { label: "已出厂", value: "6" },
    { label: "已发货", value: "7" },
    { label: "已收货", value: "8" },
    { label: "已评价", value: "9" },
    { label: "已完成", value: "10" },
    { label: "已取消", value: "-1" },
  ];
  const orderBackground = {
    "-1": "#EB1D38",
    0: "#AEAEAE",
    1: "#E8F8F3",
    2: "#81D3F8",
    3: "#9BFB99",
    4: "#F9F871",
    5: "#845EC2",
    6: "#95F204",
    7: "#FF6700",
    8: "#F2C0CD",
    9: "#EE5E91",
    10: "#0AEEB3",
  };
  let orderSourceType = {
    0: "微信小程序",
    1: "支付宝小程序",
    2: "后台订单",
    3: "到位",
    4: "到家",
    5: "美团",
    6: "口碑",
    7: "抖音",
    8: "大鲸洗",
    9: "衣卫士",
    10: "京东",
    11: "丰巢",
    12: "衣优达",
  };
  /** 获取订单状态名称
   * @param status 订单状态
   * @returns 订单状态名称
   */
  const getOrderStatusName = (status: string) => {
    const obj = orderStatus.find((item) => item.value === status);
    return obj?.label ?? "";
  };
  /** 获取订单背景颜色
   * @param status 订单状态
   * @returns 订单背景颜色
   */
  const getOrderBackground = (status: string) => {
    return orderBackground[status];
  };
  /** 获取订单来源类型名称
   * @param sourceType 订单来源类型
   * @returns 订单来源类型名称
   */
  const getOrderSourceTypeName = (sourceType: string) => {
    return orderSourceType[sourceType];
  };
  /** 获取订单列表数据
   * @param params 查询参数
   * @returns 订单列表数据
   */
  const fetchOrderData = async (params: {
    current: number;
    size: number;
    [key: string]: any;
  }): Promise<any> => {
    const { data: res } = await fetchList({
      ...params,
      factoryId: factoryId.value,
    });
    orderList.value = res.data.records;
    return res.data;
  };
  /** 获取订单详情数据
   * @param id 订单ID
   * @returns 订单详情数据
   */
  const fetchOrderDetail = async (id: string | number) => {
    const { data: res } = await getObj(id);
    return res.data;
  };
  return {
    getOrderStatusName,
    getOrderBackground,
    getOrderSourceTypeName,
    fetchOrderData,
    fetchOrderDetail,
  };
}
